import request from '@/utils/request'

// 查询闸门操作运行列表
export function listDor(query) {
  return request({
    url: '/matrix/dor/list',
    method: 'get',
    params: query
  })
}

// 查询闸门操作运行详细
export function getDor(sid) {
  return request({
    url: '/matrix/dor/' + sid,
    method: 'get'
  })
}

// 新增闸门操作运行
export function addDor(data) {
  return request({
    url: '/matrix/dor',
    method: 'post',
    data: data
  })
}

// 修改闸门操作运行
export function updateDor(data) {
  return request({
    url: '/matrix/dor',
    method: 'put',
    data: data
  })
}

// 删除闸门操作运行
export function delDor(sid) {
  return request({
    url: '/matrix/dor/' + sid,
    method: 'delete'
  })
}

// 查询水利工程列表
export function listProj() {
  return request({
    url: '/matrix/dor/list_proj',
    method: 'get'
  })
}
