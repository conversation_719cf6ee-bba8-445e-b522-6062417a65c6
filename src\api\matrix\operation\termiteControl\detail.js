import request from '@/utils/request'

// 查询白蚁防治明细列表
export function listDetail(query) {
  return request({
    url: '/matrix/detail/list',
    method: 'get',
    params: query
  })
}

// 查询白蚁防治明细详细
export function getDetail(id) {
  return request({
    url: '/matrix/detail/' + id,
    method: 'get'
  })
}

// 新增白蚁防治明细
export function addDetail(data) {
  return request({
    url: '/matrix/detail',
    method: 'post',
    data: data
  })
}

// 修改白蚁防治明细
export function updateDetail(data) {
  return request({
    url: '/matrix/detail',
    method: 'put',
    data: data
  })
}

// 删除白蚁防治明细
export function delDetail(id) {
  return request({
    url: '/matrix/detail/' + id,
    method: 'delete'
  })
}

//根据工程地段模糊查询
export function likeEngineeringSite(params) {
  return request({
    url: '/matrix/detail/likeEngineeringSite',
    method: 'get',
    params: params
  })
}
