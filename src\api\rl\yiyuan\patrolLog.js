import request from '@/utils/request'

// 查询巡河日志列表
export function listPatrolLog(query) {
  return request({
    url: '/rl/patrolLog/list',
    method: 'get',
    params: query
  })
}

// 查询巡河日志详细
export function getPatrolLog(id) {
  return request({
    url: '/rl/patrolLog/' + id,
    method: 'get'
  })
}

// 新增巡河日志
export function addPatrolLog(data) {
  return request({
    url: '/rl/patrolLog',
    method: 'post',
    data: data
  })
}

// 修改巡河日志
export function updatePatrolLog(data) {
  return request({
    url: '/rl/patrolLog',
    method: 'put',
    data: data
  })
}

// 删除巡河日志
export function delPatrolLog(id) {
  return request({
    url: '/rl/patrolLog/' + id,
    method: 'delete'
  })
}
