import request from '@/utils/request'

// 查询基础信息
export function baseInfo(query) {
    return request({
        url: '/matrix/mec/baseinfo',
        method: 'get',
        params: query
    })
}

// 查询水利工程列表
export function listProj() {
    return request({
        url: '/matrix/mec/list_proj',
        method: 'get',
    })
}

// 查询水位库容面积列表
export function listZvarl(query) {
    return request({
        url: '/matrix/mec/list_stzvarl',
        method: 'get',
        params: query
    })
}

// 新增水位库容面积列表
export function addZvarl(data) {
    return request({
        url: '/matrix/mec/stzvarl',
        method: 'post',
        data: data
    })
}

//更新水位库容面积列表
export function updateZvarl(data) {
    return request({
        url: '/matrix/mec/stzvarl',
        method: 'put',
        data: data
    })
}

// 删除水位库容面积列表
export function delZvarl(data) {
    return request({
        url: '/matrix/mec/stzvarl',
        method: 'delete',
        data: data
    })
}

// 查询闸门开度泄量列表
export function listGod(query) {
    return request({
        url: '/matrix/mec/list_god',
        method: 'get',
        params: query
    })
}

// 新增闸门开度泄量列表
export function addGod(data) {
    return request({
        url: '/matrix/mec/god',
        method: 'post',
        data: data
    })
}

// 更新闸门开度泄量列表
export function updateGod(data) {
    return request({
        url: '/matrix/mec/god',
        method: 'put',
        data: data
    })
}

// 删除闸门开度泄量列表
export function delGod(data) {
    return request({
        url: '/matrix/mec/god',
        method: 'delete',
        data: data
    })
}


