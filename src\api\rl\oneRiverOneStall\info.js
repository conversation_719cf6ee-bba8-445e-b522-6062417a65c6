import request from '@/utils/request'



// 查询一河一档-分类动态信息-堤防信息详细
export function getInfo(id) {
  return request({
    url: '/rl/water/dynamic/' + id,
    method: 'get'
  })
}

// 新增一河一档-分类动态信息-堤防信息
export function addInfo(data) {
  return request({
    url: '/rl/water/dynamic',
    method: 'post',
    data: data
  })
}

// 修改一河一档-分类动态信息-堤防信息
export function updateInfo(data) {
  return request({
    url: '/rl/water/dynamic',
    method: 'put',
    data: data
  })
}

// 删除一河一档-分类动态信息-堤防信息
export function delInfo(id) {
  return request({
    url: '/rl/water/dynamic/' + id,
    method: 'delete'
  })
}
