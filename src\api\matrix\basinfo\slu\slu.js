import request from '@/utils/request'

// 查询水闸基本信息列表
export function listSlu(query) {
  return request({
    url: '/matrix/slu/list',
    method: 'get',
    params: query
  })
}

// 查询水闸基本信息详细
export function getSlu(bid) {
  return request({
    url: '/matrix/slu/' + bid,
    method: 'get'
  })
}

// 新增水闸基本信息
export function addSlu(data) {
  return request({
    url: '/matrix/slu',
    method: 'post',
    data: data
  })
}

// 修改水闸基本信息
export function updateSlu(data) {
  return request({
    url: '/matrix/slu',
    method: 'put',
    data: data
  })
}

// 删除水闸基本信息
export function delSlu(bid) {
  return request({
    url: '/matrix/slu/' + bid,
    method: 'delete'
  })
}

//工程特性详细
export function proCharacterDetail(bid) {
  return request({
    url: '/matrix/enst/' + bid,
    method: 'get'
  })
}

//工程特性更新
export function proCharacterUpdate(data) {
  return request({
    url: '/matrix/enst',
    method: 'put',
    data:data
  })
}

//工程特性新增
export function proCharacterAdd(data) {
  return request({
    url: '/matrix/enst',
    method: 'post',
    data:data
  })
}

//水文特性详细
export function hyCharacterDetail(bid) {
  return request({
    url: '/matrix/szsw/' + bid,
    method: 'get'
  })
}

//水文特性更新
export function hyCharacterUpdate(data) {
  return request({
    url: '/matrix/szsw',
    method: 'put',
    data:data
  })
}

//水文特性新增
export function hyCharacterAdd(data) {
  return request({
    url: '/matrix/szsw',
    method: 'post',
    data:data
  })
}



// 查询水闸断面信息列表
export function listSectionSlu(query) {
  return request({
    url: '/matrix/ecs/list',
    method: 'get',
    params: query
  })
}
// 查询水闸断面信息详细
export function getSectionSluDetail(eid) {
  return request({
    url: '/matrix/ecs/' + eid,
    method: 'get'
  })
}

//水闸断面新增
export function addSectionSlu(data) {
  return request({
    url: '/matrix/ecs',
    method: 'post',
    data:data
  })
}

//水闸断面更新
export function updateSectionSlu(data) {
  return request({
    url: '/matrix/ecs',
    method: 'put',
    data:data
  })
}

//水闸断面删除
export function delSectionSlu(eid) {
  return request({
    url: '/matrix/ecs/' + eid,
    method: 'delete'
  })
}
