import request from '@/utils/request'

//四管-维养统计数量
export function maintain_statis(query) {
  return request({
    url: '/matrix/baseline/siguan/maintain_statis',
    method: 'get',
    params: query
  })
}

//四管-维修养护列表
export function maint_list(query) {
  return request({
    url: '/matrix/baseline/siguan/maint_list',
    method: 'get',
    params: query
  })
}
//四管-供水管理
export function water_supply_type(query) {
  return request({
    url: '/matrix/baseline/siguan/water_supply_type',
    method: 'get',
    params: query
  })
}

//四管-清淤治理
export function sile_statistic(query) {
  return request({
    url: '/matrix/baseline/siguan/sile_statistic',
    method: 'get',
    params: query
  })
}
//四管-白蚁防治
export function termite_control_statistic(query) {
  return request({
    url: '/matrix/baseline/siguan/termite_control_statistic',
    method: 'get',
    params: query
  })
}

//四管-白蚁防治
export function popup_door_list(query) {
  return request({
    url: '/matrix/baseline/siguan/popup_door_list',
    method: 'get',
    params: query
  })
}

//四管-害堤动物
export function dikeanimal_stat(query) {
  return request({
    url: '/matrix/baseline/siguan/dikeanimal_stat',
    method: 'get',
    params: query
  })
}


//四管-调度运用流量
export function operation(query) {
  return request({
    url: '/matrix/baseline/siguan/operation',
    method: 'get',
    params: query
  })
}
//四管-社会效益
export function economic_analy(query) {
  return request({
    url: '/matrix/baseline/siguan/economic_analy',
    method: 'get',
    params: query
  })
}

