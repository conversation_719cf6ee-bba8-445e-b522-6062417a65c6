import request from '@/utils/request'

// 查询一河一档河流支流信息列表
export function listZl(query) {
    return request({
        url: '/rl/water/zl/list',
        method: 'get',
        params: query
    })
}

// 查询一河一档河流支流信息详细
export function getZl(id) {
    return request({
        url: '/rl/water/zl/' + id,
        method: 'get'
    })
}

// 新增一河一档河流支流信息
export function addZl(data) {
    return request({
        url: '/rl/water/zl',
        method: 'post',
        data: data
    })
}

// 修改一河一档河流支流信息
export function updateZl(data) {
    return request({
        url: '/rl/water/zl',
        method: 'put',
        data: data
    })
}

// 删除一河一档河流支流信息
export function delZl(id) {
    return request({
        url: '/rl/water/zl/' + id,
        method: 'delete'
    })
}
