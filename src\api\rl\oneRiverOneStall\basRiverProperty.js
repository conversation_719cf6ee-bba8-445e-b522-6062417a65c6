import request from '@/utils/request'

// 查询河流自然属性列表
export function listProperty(query) {
    return request({
        url: '/rl/water/basRiverProperty/list',
        method: 'get',
        params: query
    })
}

// 查询河流自然属性详细
export function getProperty(rvcd) {
    return request({
        url: '/rl/water/basRiverProperty/' + rvcd,
        method: 'get'
    })
}

// 新增河流自然属性
export function addProperty(data) {
    return request({
        url: '/rl/water/basRiverProperty',
        method: 'post',
        data: data
    })
}

// 修改河流自然属性
export function updateProperty(data) {
    return request({
        url: '/rl/water/basRiverProperty',
        method: 'put',
        data: data
    })
}

// 删除河流自然属性
export function delProperty(rvcd) {
    return request({
        url: '/rl/water/basRiverProperty/' + rvcd,
        method: 'delete'
    })
}
