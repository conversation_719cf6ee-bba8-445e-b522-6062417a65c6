import request from '@/utils/request'

// 查询物资仓库绑定列表
export function listWzBinding(query) {
  return request({
    url: '/matrix/wzBinding/list',
    method: 'get',
    params: query
  })
}

// 查询物资仓库绑定详细
export function getWzBinding(id) {
  return request({
    url: '/matrix/wzBinding/' + id,
    method: 'get'
  })
}

// 新增物资仓库绑定
export function addWzBinding(data) {
  return request({
    url: '/matrix/wzBinding',
    method: 'post',
    data: data
  })
}

// 修改物资仓库绑定
export function updateWzBinding(data) {
  return request({
    url: '/matrix/wzBinding',
    method: 'put',
    data: data
  })
}

// 删除物资仓库绑定
export function delWzBinding(id) {
  return request({
    url: '/matrix/wzBinding/' + id,
    method: 'delete'
  })
}
