import request from '@/utils/request'

// 查询涉河建设列表
export function riverRelatedList(query) {
    return request({
      url: '/rl/legalPro/getAllLegalPro',
      method: 'get',
      params: query
    })
  }
// 查询单个涉河建设项目详细信息
export function riverRelatedDetail1(query) {
  return request({
    url: '/rl/legalPro/getLegalProById',
    method: 'get',
    params: query
  })
}
export function addNet(data){
  return request({
    url:'/rl/legalPro',
    method:'post',
    params:data
  })
}
export function updateNet(data){
  return request({
    url:'/rl/legalPro',
    method:'put',
    params:data
  })
}
export function delNet(id){
  return request({
    url:'/rl/legalPro/' + id,
    method:'delete'
  })
}


// 项目进展查询
export function selectBasLegalProjectZiboNetInfo(params) {
  return request({
    url: "/rl/legalPro/selectBasLegalProjectZiboNetInfo/",
    method: "get",
    params,
  });
}

//新增项目进展
export function insertSomRsrfSign(params) {
  return request({
    url: "/rl/legalPro/insertSomRsrfSign/",
    method: "post",
    params,
  });
}

//删除项目进展
export function removeXmjz(id) {
  return request({
    url: "/rl/legalPro/removeXmjz/" + id,
    method: "delete",
  });
}
