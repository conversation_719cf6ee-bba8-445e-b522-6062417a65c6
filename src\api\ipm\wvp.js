import request from '@/utils/request'

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/wvp/api/device/query/devices',
    method: 'get',
    params: query
  })
}

// 查询通道列表
export function listChannel(query) {
  return request({
    url: '/wvp/api/device/query/devices/' + query.deviceId + '/channels',
    method: 'get',
    params: query
  })
}

// 点播
export function play(deviceId, channelId) {
  return request({
    url: `/wvp/api/play/start/${deviceId}/${channelId}`,
    method: 'get'
  })
}
