import request from '@/utils/request'

// 查询平台管理-巡查管理-巡查配置（巡查任务）列表
export function listManage(query) {
  return request({
    url: '/rl/water/patrolTaskManage/list',
    method: 'get',
    params: query
  })
}

// 查询平台管理-巡查管理-巡查配置（巡查任务）列表
export function listAllManage(query) {
  return request({
    url: '/rl/water/patrolTaskManage/allList',
    method: 'get',
    params: query
  })
}

// 查询平台管理-巡查管理-巡查配置（巡查任务）详细
export function getManage(id) {
  return request({
    url: '/rl/water/patrolTaskManage/' + id,
    method: 'get'
  })
}

// 新增平台管理-巡查管理-巡查配置（巡查任务）
export function addManage(data) {
  return request({
    url: '/rl/water/patrolTaskManage',
    method: 'post',
    data: data
  })
}

// 修改平台管理-巡查管理-巡查配置（巡查任务）
export function updateManage(data) {
  return request({
    url: '/rl/water/patrolTaskManage',
    method: 'put',
    data: data
  })
}

// 删除平台管理-巡查管理-巡查配置（巡查任务）
export function delManage(id) {
  return request({
    url: '/rl/water/patrolTaskManage/' + id,
    method: 'delete'
  })
}

// 查询水库、山洪村、塘坝下拉树结构
export function treeSelect() {
  return request({
    url: '/rl/water/patrolTaskManage/treeSelect',
    method: 'get'
  })
}

// 查询山洪村下拉树结构
export function villageTreeSelect() {
  return request({
    url: '/rl/water/patrolTaskManage/villageTreeSelect',
    method: 'get'
  })
}

// 查询塘坝下拉树结构
export function tangbaTreeSelect() {
  return request({
    url: '/rl/water/patrolTaskManage/tangbaTreeSelect',
    method: 'get'
  })
}

export function getRequest(id) {
  return request({
    url: '/rl/water/patrolTaskManage/requestSelect/'+id,
    method: 'get'
  })
}

export function getReq(id) {
  return request({
    url: '/rl/water/patrolTaskManage/reqSelect/' + id,
    method: 'get'
  })
}

//查询河流下拉树结构
export function treeRiverSelect() {
    return request({
        url: '/rl/water/patrolTaskManage/treeRiverSelect',
        method: 'get'
    })
}

// 查询水库下拉树结构
export function treeReservoirSelect() {
    return request({
        url: '/rl/water/patrolTaskManage/treeReservoirSelect',
        method: 'get'
    })
}
