import request from "../../utils/request";

//工程列表
export function projectList(){
  return request({
    url: '/matrix/project/list',
    method: 'get'
  })
}
//工程及其类型列表
export function projectTypeList(){
  return request({
    url: '/matrix/project/list1',
    method: 'get'
  })
}
//工程及其类型列表
export function listProjByType(type){
  return request({
    url: '/matrix/project/list_proj_by_type',
    method: 'get',
    params: {type}
  })
}



//测站列表
export function stationList(data){
  return request({
    url: '/matrix/alarm/rain_station',
    method: 'get',
    params: data
  })
}
