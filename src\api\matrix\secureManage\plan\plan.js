import request from '@/utils/request'

// 查询预案管理列表
export function listPlan(query) {
  return request({
    url: '/matrix/smPlan/list',
    method: 'get',
    params: query
  })
}
// 查询【预案管理】列表 新
export function listPreplan(query) {
  return request({
    url: '/zhy-forecast/b/preplan/list',
    method: 'get',
    params: query
  })
}
// 查询预案管理详细
export function getPlan(pid) {
  return request({
    url: '/matrix/smPlan/' + pid,
    method: 'get'
  })
}

// 新增预案管理
export function addPlan(data) {
  return request({
    url: '/matrix/smPlan',
    method: 'post',
    data: data
  })
}

// 修改预案管理
export function updatePlan(data) {
  return request({
    url: '/matrix/smPlan',
    method: 'put',
    data: data
  })
}

// 删除预案管理
export function delPlan(pid) {
  return request({
    url: '/matrix/smPlan/' + pid,
    method: 'delete'
  })
}
