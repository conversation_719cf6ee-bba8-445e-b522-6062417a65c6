import request from '@/utils/request'

// 查询界碑界桩列表
export function listPillar(query) {
  return request({
    url: '/matrix/pillar/list',
    method: 'get',
    params: query
  })
}

// 查询界碑界桩详细
export function getPillar(pid) {
  return request({
    url: '/matrix/pillar/' + pid,
    method: 'get'
  })
}

// 新增界碑界桩
export function addPillar(data) {
  return request({
    url: '/matrix/pillar',
    method: 'post',
    data: data
  })
}

// 修改界碑界桩
export function updatePillar(data) {
  return request({
    url: '/matrix/pillar',
    method: 'put',
    data: data
  })
}

// 删除界碑界桩
export function delPillar(pid) {
  return request({
    url: '/matrix/pillar/' + pid,
    method: 'delete'
  })
}
