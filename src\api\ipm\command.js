import request from '@/utils/request'

// 查询媒体配置列表
export function listProfile(query) {
  return request({
    url: '/ipm/command/get_media_profiles',
    method: 'get',
    params: query
  })
}

// 查询预置位列表
export function listPreset(query) {
  return request({
    url: '/ipm/command/get_presets',
    method: 'get',
    params: query
  })
}

// 获取快照
export function getSnapshot(query) {
  return request({
    url: '/ipm/command/get_snapshot',
    method: 'get',
    responseType: 'blob',
    params: query
  })
}
