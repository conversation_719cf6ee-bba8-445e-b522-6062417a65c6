import request from '@/utils/request'

// 查询洪水影响范围列表
export function listFloodArea(query) {
  return request({
    url: '/matrix/floodArea/list',
    method: 'get',
    params: query
  })
}

// 查询洪水影响范围详细
export function getFloodArea(id) {
  return request({
    url: '/matrix/floodArea/' + id,
    method: 'get'
  })
}

// 新增洪水影响范围
export function addFloodArea(data) {
  return request({
    url: '/matrix/floodArea',
    method: 'post',
    data: data
  })
}

// 修改洪水影响范围
export function updateFloodArea(data) {
  return request({
    url: '/matrix/floodArea',
    method: 'put',
    data: data
  })
}

// 删除洪水影响范围
export function delFloodArea(id) {
  return request({
    url: '/matrix/floodArea/' + id,
    method: 'delete'
  })
}

// 获取保护对象
export function getProtectedVillageAll() {
  return request({
    url: '/matrix/floodArea/protected_all',
    method: 'get'
  })
}
// 获取洪水影响的保护对象
export function getProtectedVillage(query) {
  return request({
    url: '/matrix/floodArea/flood_protected',
    method: 'get',
    params:query
  })
}
