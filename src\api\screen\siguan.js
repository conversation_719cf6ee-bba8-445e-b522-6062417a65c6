import request from '@/utils/request'

//四管-大屏中间-运行管理
export function operation() {
  return request({
    url: '/matrix/siguan/operation',
    method: 'get',
  })
}
// 四管-安全鉴定
export function appraisal_info() {
  return request({
    url: '/matrix/siguan/appraisal_info',
    method: 'get',
  })
}

//四管-工程巡查
export function inspection() {
  return request({
    url: '/matrix/siguan/inspection',
    method: 'get',
  })
}

//四管-维修养护
export function repair() {
  return request({
    url: '/matrix/siguan/repair',
    method: 'get',
  })
}

//四管-安全检查
export function check_info(startTime, endTime) {
  return request({
    url: '/matrix/siguan/check_info?startTime=' + startTime + '&endTime=' + endTime,
    method: 'get',
  })
}

//四管-应急保障-队伍
export function team() {
  return request({
    url: '/matrix/siguan/team',
    method: 'get',
  })
}

//四管-应急保障-物资
export function material() {
  return request({
    url: '/matrix/siguan/material',
    method: 'get',
  })
}

//四管-应急保障-专家
export function expert() {
  return request({
    url: '/matrix/siguan/expert',
    method: 'get',
  })
}

//四管-工程巡查-二级弹窗 计划巡视
export function plans(query) {
  return request({
    url: '/matrix/siguan/plans',
    method: 'get',
    params: query
  })
}

//四管-工程巡查-二级弹窗 已巡查
export function records(query) {
  return request({
    url: '/matrix/siguan/records',
    method: 'get',
    params: query
  })
}

//四管-工程巡查-二级弹窗 隐患列表
export function dangers(query) {
  return request({
    url: '/matrix/siguan/dangers',
    method: 'get',
    params: query
  })
}

//四管-维修养护-二级弹窗 年度
export function amc(query) {
  return request({
    url: '/matrix/siguan/amc',
    method: 'get',
    params: query
  })
}

//四管-维修养护-二级弹窗 专项
export function maint(query) {
  return request({
    url: '/matrix/siguan/maint',
    method: 'get',
    params: query
  })
}

//四管-维修养护-二级弹窗 日常
export function dmau(query) {
  return request({
    url: '/matrix/siguan/dmau',
    method: 'get',
    params: query
  })
}

//四管-安全检查-二级弹窗 列表
export function check_list(query) {
  return request({
    url: '/matrix/siguan/check_list',
    method: 'get',
    params: query
  })
}

//四管-应急保障-队伍
export function rescue_team(query) {
  return request({
    url: '/matrix/rescue-team/list',
    method: 'get',
    params: query
  })
}

//四管-应急保障-物资
export function mtManage(query) {
  return request({
    url: '/matrix/wh/mtManage/list',
    method: 'get',
    params: query
  })
}

//四管-应急保障-专家
export function experts_list(query) {
  return request({
    url: '/matrix/experts/list',
    method: 'get',
    params: query
  })
}






