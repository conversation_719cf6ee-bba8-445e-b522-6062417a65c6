import request from '@/utils/request'

// 查询取水口撒点列表
export function listIntake(query) {
  return request({
    url: '/product/intake/list',
    method: 'get',
    params: query
  })
}

// 查询取水口撒点详细
export function getIntake(id) {
  return request({
    url: '/product/intake/' + id,
    method: 'get'
  })
}

// 新增取水口撒点
export function addIntake(data) {
  return request({
    url: '/product/intake',
    method: 'post',
    data: data
  })
}

// 修改取水口撒点
export function updateIntake(data) {
  return request({
    url: '/product/intake',
    method: 'put',
    data: data
  })
}

// 删除取水口撒点
export function delIntake(id) {
  return request({
    url: '/product/intake/' + id,
    method: 'delete'
  })
}
