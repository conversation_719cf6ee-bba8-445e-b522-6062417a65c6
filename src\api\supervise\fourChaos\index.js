import request from '@/utils/request'

// 查询总问题列表
export function getMessSummary() {
  return request({
    url: '/product/fourMess/getMessSummary' ,
    method: 'get'
  })
}

//查询四乱
export function getFourMesses(query) {
  return request({
    url: '/product/fourMess/selectFourMesses',
    method: 'get',
    params: query
  })
}

//图例数据
export function getLegendData() {
  return request({
    url: '/product/fourMess/getRiverMessTotal',
    method: 'get'
  })
}

// 问题排名
export function getMaxProblemType() {
  return request({
    url: '/product/fourMess/getMaxProblemType',
    method: 'get'
  })
}

// 查询所有正常使用的行政区域列表
export function listNormalArea() {
  return request({
      url: '/product/water/area/listArea',
      method: 'get',
  })
}

// 查询河流管理列表
export function listRiverZibo(query) {
  return request({
    url: '/product/riverZibo/list',
    method: 'get',
    params: query
  })
}

export function problemDetail(query) {
  return request({
    url: "/product/spaceElement/getRiverQuestionSec",
    method: "get",
    params: query
  })
}
