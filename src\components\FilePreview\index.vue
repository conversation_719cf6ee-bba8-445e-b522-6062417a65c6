<template>
  <div :style="'width: 100%; height: ' + height + 'px;'" v-loading="loading">
    <el-image
      v-if="type === 'image'"
      :src="src"
      :preview-src-list="this.previewSrcList ? [this.src] : null"
      fit="contain"
      style="width: 100%; height: 100%;display: block;"
      @load="loading = false"
      @error="loading = false"
    >
      <div slot="error" class="image-slot">
        <i class="el-icon-picture-outline"></i>
      </div>
    </el-image>
    <document-editor
      v-else-if="type === 'document' && key"
      ref="documentEditor"
      action="view"
      :config="config"
      :document-server="documentServer"
      :identifier="key"
      :name="name"
      :secret="secret"
      :url="src"
      :callback-url="callbackUrl"
      :username="username"
      @onAppReady="onDocumentReady"
      @onError="onDocumentError"
    />
    <iframe
      v-else-if="type === 'unknown'"
      ref="iframe"
      :src="src"
      :title="name"
      width="100%"
      height="100%"
    />
  </div>
</template>

<script>
import DocumentEditor from "@zhy/document-editor";
import {getFileIdentifier} from "@/api/file";

export default {
  name: 'FilePreview',
  components: {DocumentEditor},
  props: {
    name: {
      type: String,
      default: null,
      required: true
    },
    url: {
      type: String,
      default: null,
      required: true
    },
    height: {
      type: Number,
      default: 600
    },
    relativePath: {
      type: Boolean,
      default: true
    },
    previewSrcList: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      documentTypes: ['.csv', '.djvu', '.doc', '.docm', '.docx', '.docxf', '.dot', '.dotm', '.dotx',
        '.epub', '.fb2', '.fodp', '.fods', '.fodt', '.htm', '.html', '.mht', '.odp', '.ods', '.odt',
        '.oform', '.otp', '.ots', '.ott', '.oxps', '.pdf', '.pot', '.potm', '.potx', '.pps', '.ppsm',
        '.ppsx', '.ppt', '.pptm', '.pptx', '.rtf', '.txt', '.xls', '.xlsb', '.xlsm', '.xlsx', '.xlt',
        '.xltm', '.xltx', '.xml', '.xps'],
      imageTypes: ['.jpg', '.jpeg', '.png', '.gif'],
      documentServer: 'https://www.zhywater.com:40001',
      key: null,
      secret: "zhy",
      config: {
        editorConfig: {
          mode: "view",
          customization: {
            close: {
              visible: true,
              text: "关闭文件"
            },
            customer: {
              address: "山东省淄博高新区仪器仪表产业园10号楼",
              logo: "https://www.zhywater.com:40001/web-apps/logo/logo2.svg",
              mail: "<EMAIL>",
              name: "智洋创新科技股份有限公司",
              phone: "0533-3586816",
              www: "http://www.zhiyang.com.cn"
            },
            hideRightMenu: true,
            logo: {
              image: "https://www.zhywater.com:40001/web-apps/logo/logo.svg",
              url: "http://www.zhiyang.com.cn"
            },
            uiTheme: "theme-classic-light"
          }
        },
        events: {
          onRequestEditRights: this.onRequestEditRights,
          onRequestClose: this.onRequestClose,
          onRequestUsers: this.onRequestUsers
        }
      }
    }
  },
  computed: {
    type() {
      let ext = this.name.substring(this.name.lastIndexOf('.')).toLowerCase()
      if (this.imageTypes.includes(ext)) {
        return 'image'
      } else if (this.documentTypes.includes(ext)) {
        return 'document'
      } else {
        return 'unknown'
      }
    },
    username() {
      let nickName = this.$store.state.user.nickName;
      let username = this.$store.state.user.name;
      return nickName || username || "匿名";
    },
    src() {
      if (!this.relativePath) {
        return this.url
      }
      let host = window.location.origin + process.env.VUE_APP_BASE_API
      return host + '/file/static' + this.url
    },
    callbackUrl() {
      let host = window.location.origin + process.env.VUE_APP_BASE_API
      return host + "/file/callback?name=" + this.name + "&url=" + this.url;
    },
    change() {
      return this.name + this.url + this.height;
    }
  },
  watch: {
    change: {
      handler() {
        this.key = null;
        this.getFileKey(this.url);
      },
      immediate: true
    }
  },
  mounted() {
    if (this.type === 'unknown') {
      const iframe = this.$refs.iframe
      iframe.onload = () => {
        this.loading = false;
      }
      iframe.onerror = () => {
        this.loading = false;
      }
    }
  },
  methods: {
    async getFileKey(url) {
      const response = await getFileIdentifier({url: url});
      this.key = response.data;
    },
    onDocumentReady() {
      this.loading = false;
    },
    onDocumentError() {
      this.loading = false;
    },
    onRequestEditRights() {
      this.config.editorConfig.mode = "edit";
    },
    onRequestClose() {
      this.$emit("close");
    },
    onRequestUsers(event) {
      let data = event.data;
      let userId = require("md5")(this.username);

      if (data.c === "info" && data.id.indexOf(userId) !== -1) {
        let imageUrl = this.$store.state.user.avatar.startsWith(window.location.origin)
          ? this.$store.state.user.avatar
          : window.location.origin + this.$store.state.user.avatar;
        this.convertImageToBase64(imageUrl, (image) => {
          this.$refs.documentEditor.docEditor.setUsers({
            c: data.c,
            users: [{id: userId, image: image}]
          });
        });
      }
    },
    convertImageToBase64(imageUrl, callback) {
      fetch(imageUrl)
        .then(response => response.blob())
        .then(blob => {
          const reader = new FileReader();
          reader.onloadend = function () {
            callback(reader.result);
          };
          reader.readAsDataURL(blob);
        })
        .catch(_ => {
          callback(null);
        });
    }
  }
}
</script>
