import request from '@/utils/request'

// 查询泵站信息列表
export function listPump(query) {
  return request({
    url: '/matrix/pump/list',
    method: 'get',
    params: query
  })
}

// 查询泵站信息详细
export function getPump(stationId) {
  return request({
    url: '/matrix/pump/' + stationId,
    method: 'get'
  })
}

// 新增泵站信息
export function addPump(data) {
  return request({
    url: '/matrix/pump',
    method: 'post',
    data: data
  })
}

// 修改泵站信息
export function updatePump(data) {
  return request({
    url: '/matrix/pump',
    method: 'put',
    data: data
  })
}

// 删除泵站信息
export function delPump(stationId) {
  return request({
    url: '/matrix/pump/' + stationId,
    method: 'delete'
  })
}

// 根据工程ID查询泵站信息列表
export function listPumpByEncd(encd) {
  return request({
    url: '/matrix/pump/encd/' + encd,
    method: 'get',
  })
}

// 新增泵站工程关联信息
export function addPumpWithEncd(data) {
  return request({
    url: '/matrix/pump/encd',
    method: 'post',
    data: data
  })
}

// 查询水库基本信息列表
export function listBsin(query) {
  return request({
    url: '/matrix/pump/wrpbin',
    method: 'get',
    params: query
  })
}