import request from '@/utils/request'

// 查询塘坝工程管理列表
export function listPond(query) {
    return request({
        url: '/rl/water/tangba/list',
        method: 'get',
        params: query
    })
}

// 查询塘坝工程管理详细
export function getPond(tbId) {
    return request({
        url: '/rl/water/tangba/' + tbId,
        method: 'get'
    })
}

// 新增塘坝工程管理
export function addPond(data) {
    return request({
        url: '/rl/water/tangba',
        method: 'post',
        data: data
    })
}

// 修改塘坝工程管理
export function updatePond(data) {
    return request({
        url: '/rl/water/tangba',
        method: 'put',
        data: data
    })
}

// 删除塘坝工程管理
export function delPond(tbId) {
    return request({
        url: '/rl/water/tangba/' + tbId,
        method: 'delete'
    })
}
