import request from '@/utils/request'

// 查询人工检测水质列表
export function listArtificial(query) {
  return request({
    url: '/matrix/artificial/list',
    method: 'get',
    params: query
  })
}

// 查询人工检测水质详细
export function getArtificial(bid) {
  return request({
    url: '/matrix/artificial/' + bid,
    method: 'get'
  })
}

// 新增人工检测水质
export function addArtificial(data) {
  return request({
    url: '/matrix/artificial',
    method: 'post',
    data: data
  })
}

// 修改人工检测水质
export function updateArtificial(data) {
  return request({
    url: '/matrix/artificial',
    method: 'put',
    data: data
  })
}

// 删除人工检测水质
export function delArtificial(bid) {
  return request({
    url: '/matrix/artificial/' + bid,
    method: 'delete'
  })
}
// 查询最新一次检测记录
export function listManDetRecently(data) {
  return request({
    url: '/matrix/artificial/listlast',
    method: 'get',
    params: data
  })
}
