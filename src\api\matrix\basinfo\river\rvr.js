import request from '@/utils/request'

// 查询河流基本信息列表
export function listRvr(query) {
  return request({
    url: '/matrix/rvr/list',
    method: 'get',
    params: query
  })
}

// 查询河流基本信息详细
export function getRvr(rvcd) {
  return request({
    url: '/matrix/rvr/' + rvcd,
    method: 'get'
  })
}

// 新增河流基本信息
export function addRvr(data) {
  return request({
    url: '/matrix/rvr',
    method: 'post',
    data: data
  })
}

// 修改河流基本信息
export function updateRvr(data) {
  return request({
    url: '/matrix/rvr',
    method: 'put',
    data: data
  })
}

// 删除河流基本信息
export function delRvr(rvcd) {
  return request({
    url: '/matrix/rvr/' + rvcd,
    method: 'delete'
  })
}
