import request from '@/utils/request'

// 查询拦河闸列表
export function listSliuce(query) {
  return request({
    url: '/rl/sliuce/list',
    method: 'get',
    params: query
  })
}

// 查询拦河闸详细
export function getSliuce(id) {
  return request({
    url: '/rl/sliuce/' + id,
    method: 'get'
  })
}

// 新增拦河闸
export function addSliuce(data) {
  return request({
    url: '/rl/sliuce',
    method: 'post',
    data: data
  })
}

// 修改拦河闸
export function updateSliuce(data) {
  return request({
    url: '/rl/sliuce',
    method: 'put',
    data: data
  })
}

// 删除拦河闸
export function delSliuce(id) {
  return request({
    url: '/rl/sliuce/' + id,
    method: 'delete'
  })
}
