import request from '@/utils/request'

// 查询清理淤积列表
export function listSedimentControl(query) {
  return request({
    url: '/matrix/sedimentControl/list',
    method: 'get',
    params: query
  })
}

// 查询清理淤积详细
export function getSedimentControl(id) {
  return request({
    url: '/matrix/sedimentControl/' + id,
    method: 'get'
  })
}

// 新增清理淤积
export function addSedimentControl(data) {
  return request({
    url: '/matrix/sedimentControl',
    method: 'post',
    data: data
  })
}

// 修改清理淤积
export function updateSedimentControl(data) {
  return request({
    url: '/matrix/sedimentControl',
    method: 'put',
    data: data
  })
}

// 删除清理淤积
export function delSedimentControl(id) {
  return request({
    url: '/matrix/sedimentControl/' + id,
    method: 'delete'
  })
}
