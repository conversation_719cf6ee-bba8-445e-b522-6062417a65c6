import request from '@/utils/request'

//四管-巡查
export function patrol_statistics(query) {
  return request({
    url: '/matrix/baseline/siguan/patrol_statistics',
    method: 'get',
    params: query
  })
}

//四管-人工巡查记录
export function record_list(query) {
  return request({
    url: '/matrix/baseline/siguan/record_list',
    method: 'get',
    params: query
  })
}

//四管-巡查点位列表
export function point_list(query) {
  return request({
    url: '/matrix/baseline/siguan/point_list',
    method: 'get',
    params: query
  })
}

//四管-巡查内容
export function record_content(query) {
  return request({
    url: '/matrix/baseline/siguan/record_content',
    method: 'get',
    params: query
  })
}

//四管-隐患列表
export function blsdanger_list(query) {
  return request({
    url: '/matrix/baseline/siguan/blsdanger_list',
    method: 'get',
    params: query
  })
}

//四管-隐患详细信息
export function danger_info(query) {
  return request({
    url: '/matrix/baseline/siguan/danger_info',
    method: 'get',
    params: query
  })
}
// //四管-大坝安全评价
export function appraisal(query) {
  return request({
    url: '/matrix/baseline/siguan/appraisal',
    method: 'get',
    params: query
  })
}

// //四管-巡查记录弹窗列表
export function popup_record_list(query) {
  return request({
    url: '/matrix/baseline/siguan/popup_record_list',
    method: 'get',
    params: query
  })
}


// //四管-巡查询安全鉴定列表
export function popup_appraisal_list(query) {
  return request({
    url: '/matrix/baseline/siguan/popup_appraisal_list',
    method: 'get',
    params: query
  })
}
