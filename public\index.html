<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <title>数字水利平台</title>

  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <!-- 引入阿里在线图标 -->
  <link
    rel="stylesheet"
    href="//at.alicdn.com/t/c/font_2726276_2m712endtr8.css"
  />

  <!--天地图-->
  <script
    src="https://api.tianditu.gov.cn/api?v=4.0&tk=68d6790a093e936919127020ff28f39c"
    type="text/javascript"
  ></script>
  <!--全景图-->
  <script src="/vtour/tour.js"></script>
  <!-- -->
  <script src="<%= BASE_URL %>js/liveplayer-lib.min.js"></script>
  <script src="<%= BASE_URL %>Cesium/Cesium.js"></script>
  <link rel="stylesheet" href="<%= BASE_URL %>Cesium/Widgets/widgets.css"/>
  <!--[if lt IE 11]>
  <script>window.location.href = '/html/ie.html';</script><![endif]-->
  <!--天地图-->
  <script src="https://api.tianditu.gov.cn/api?v=4.0&tk=9bef437ca99a23555a8a57fb9e22f07f"
          type="text/javascript"></script>
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      background: #0A3780;
    }

    #contain {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      top: 40%;
    }

    #h3 {
      color: white;
      position: absolute;
      top: 52%;
      left: 44%;
      letter-spacing: 2px;
    }

    #ring {
      width: 190px;
      height: 190px;
      border: 1px solid transparent;
      border-radius: 50%;
      position: absolute;
    }

    #ring:nth-child(1) {
      border-bottom: 8px solid rgb(255, 141, 249);
      animation: rotate1 2s linear infinite;
    }

    @keyframes rotate1 {
      from {
        transform: rotateX(50deg) rotateZ(110deg);
      }

      to {
        transform: rotateX(50deg) rotateZ(470deg);
      }
    }

    #ring:nth-child(2) {
      border-bottom: 8px solid rgb(255, 65, 106);
      animation: rotate2 2s linear infinite;
    }

    @keyframes rotate2 {
      from {
        transform: rotateX(20deg) rotateY(50deg) rotateZ(20deg);
      }

      to {
        transform: rotateX(20deg) rotateY(50deg) rotateZ(380deg);
      }
    }

    #ring:nth-child(3) {
      border-bottom: 8px solid rgb(0, 255, 255);
      animation: rotate3 2s linear infinite;
    }

    @keyframes rotate3 {
      from {
        transform: rotateX(40deg) rotateY(130deg) rotateZ(450deg);
      }

      to {
        transform: rotateX(40deg) rotateY(130deg) rotateZ(90deg);
      }
    }

    #ring:nth-child(4) {
      border-bottom: 8px solid rgb(252, 183, 55);
      animation: rotate4 2s linear infinite;
    }

    @keyframes rotate4 {
      from {
        transform: rotateX(70deg) rotateZ(270deg);
      }

      to {
        transform: rotateX(70deg) rotateZ(630deg);
      }
    }
  </style>
</head>
<body>
<!--文件字体预加载-->
<iframe title="load-fonts" style="display:none"
        src="https://www.zhywater.com:40001/web-apps/apps/api/documents/cache-scripts.html"></iframe>
<div id="app">
  <div id="loader-wrapper">
    <!--    <div id="loader"></div>-->
    <!--    <div class="loader-section section-left"></div>-->
    <!--    <div class="loader-section section-right"></div>-->
    <!--    <div class="load_title">系统资源加载中，请耐心等待</div>-->
    <div id="contain">
      <div id="ring"></div>
      <div id="ring"></div>
      <div id="ring"></div>
      <div id="ring"></div>
    </div>
    <div id="h3">系统资源加载中，请耐心等待...</div>
  </div>
</div>
</body>
</html>
