import request from '@/utils/request'

// 查询事件记录列表
export function listEventManage(query) {
  return request({
    url: '/rl/eventManage/list',
    method: 'get',
    params: query
  })
}

// 查询事件记录详细
export function getEventManage(id) {
  return request({
    url: '/rl/eventManage/' + id,
    method: 'get'
  })
}

// 新增事件记录
export function addEventManage(data) {
  return request({
    url: '/rl/eventManage',
    method: 'post',
    data: data
  })
}

// 修改事件记录
export function updateEventManage(data) {
  return request({
    url: '/rl/eventManage',
    method: 'put',
    data: data
  })
}

// 删除事件记录
export function delEventManage(id) {
  return request({
    url: '/rl/eventManage/' + id,
    method: 'delete'
  })
}
