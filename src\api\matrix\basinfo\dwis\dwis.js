import request from '@/utils/request'

// 查询险工险段列表
export function listDwis(query) {
  return request({
    url: '/matrix/dwis/list',
    method: 'get',
    params: query
  })
}

// 查询所有的堤防
export function listAllDike() {
  return request({
    url: '/matrix/dwis/listAllDike',
    method: 'get'
  })
}

// 查询险工险段详细
export function getDwis(did) {
  return request({
    url: '/matrix/dwis/' + did,
    method: 'get'
  })
}

// 新增险工险段
export function addDwis(data) {
  return request({
    url: '/matrix/dwis',
    method: 'post',
    data: data
  })
}

// 修改险工险段
export function updateDwis(data) {
  return request({
    url: '/matrix/dwis',
    method: 'put',
    data: data
  })
}

// 删除险工险段
export function delDwis(did) {
  return request({
    url: '/matrix/dwis/' + did,
    method: 'delete'
  })
}
