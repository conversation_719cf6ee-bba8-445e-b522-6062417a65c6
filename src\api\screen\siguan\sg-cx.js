import request from '@/utils/request'

//四管-隐患治理统计
export function danger(query) {
  return request({
    url: '/matrix/baseline/siguan/danger',
    method: 'get',
    params: query
  })
}

//四管-隐患列表
export function danger_list(query) {
  return request({
    url: '/matrix/baseline/siguan/danger_list',
    method: 'get',
    params: query
  })
}


//四管-除险加固信息
export function repair(query) {
  return request({
    url: '/matrix/baseline/siguan/repair',
    method: 'get',
    params: query
  })
}



//四管-隐患列表
export function popup_danger_list(query) {
  return request({
    url: '/matrix/baseline/siguan/popup_danger_list',
    method: 'get',
    params: query
  })
}
//四管-查询除险加固列表
export function popup_repair_list(query) {
  return request({
    url: '/matrix/baseline/siguan/popup_repair_list',
    method: 'get',
    params: query
  })
}

//四管-查询除险加固列表
export function appraisal(query) {
  return request({
    url: '/matrix/baseline/siguan/appraisal',
    method: 'get',
    params: query
  })
}

//四管-展示地图隐患信息
export function danger_scatter(query) {
  return request({
    url: '/matrix/baseline/siguan/danger_scatter',
    method: 'get',
    params:query
  })
}