import request from '@/utils/request'

// 查询水功能区列表
export function listFunction(query) {
  return request({
    url: '/product/riverFunction/list',
    method: 'get',
    params: query
  })
}

// 查询水功能区详细
export function getFunction(id) {
  return request({
    url: '/product/riverFunction/' + id,
    method: 'get'
  })
}

// 新增水功能区
export function addFunction(data) {
  return request({
    url: '/product/riverFunction',
    method: 'post',
    data: data
  })
}

// 修改水功能区
export function updateFunction(data) {
  return request({
    url: '/product/riverFunction',
    method: 'put',
    data: data
  })
}

// 删除水功能区
export function delFunction(id) {
  return request({
    url: '/product/riverFunction/' + id,
    method: 'delete'
  })
}
