/*
 * @Author: yangzhengyi <EMAIL>
 * @Date: 2024-12-11 11:20:34
 * @LastEditors: yangzhengyi <EMAIL>
 * @LastEditTime: 2024-12-26 14:47:17
 * @FilePath: \bx-basic-ui\src\components\DigitalTwin\mapPoint.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
//添加水文站点
export function addHydrological(){
  return request({
    url: "/zhy-forecast/b/stBprp/getHydrologicStation",
    methods: "get",
  });
}
//添加告警水文站点
export function addGJHydrological(){
  return request({
    url: "/zhy-forecast/f/stForecast/getHydrologicStation",
    methods: "get",
  });
}
//添加告警实时水文站点
export function addSSGJHydrological(params){
  return request({
    url: "/zhy-forecast/f/stForecast/getHydrologicStationSs",
    methods: "get",
    params
  });
}
//队伍撒点
export function getsecureTeam(params) {
  return request({
    url: "/map/mp/secureTeam",
    method: "get",
    params,
  });
  //仓库撒点
}export function getwareHouse(params) {
  return request({
    url: "/map/mp/wareHouse",
    method: "get",
    params,
  });
}
//预报-水情信息-水文站撒点 真实站点
export function addHydrological2(params){
  return request({
    url: "/zhy-forecast/f/stForecast/getHydrologicStationSs",
    methods: "get",
    params
  });
}
