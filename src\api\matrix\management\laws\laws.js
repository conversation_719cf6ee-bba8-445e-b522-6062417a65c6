import request from '@/utils/request'

// 查询法律法规列表
export function listLaws(query) {
  return request({
    url: '/matrix/laws/list',
    method: 'get',
    params: query
  })
}

// 查询法律法规详细
export function getLaws(lid) {
  return request({
    url: '/matrix/laws/' + lid,
    method: 'get'
  })
}

// 新增法律法规
export function addLaws(data) {
  return request({
    url: '/matrix/laws',
    method: 'post',
    data: data
  })
}

// 修改法律法规
export function updateLaws(data) {
  return request({
    url: '/matrix/laws',
    method: 'put',
    data: data
  })
}

// 删除法律法规
export function delLaws(lid) {
  return request({
    url: '/matrix/laws/' + lid,
    method: 'delete'
  })
}
