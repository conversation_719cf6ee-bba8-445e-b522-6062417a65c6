import request from '@/utils/request'

// 查询评价管理列表
export function listEvalManage(query) {
  return request({
    url: '/matrix/evalManage/list',
    method: 'get',
    params: query
  })
}

// 查询评价管理详细
export function getEvalManage(eid) {
  return request({
    url: '/matrix/evalManage/' + eid,
    method: 'get'
  })
}

// 新增评价管理
export function addEvalManage(data) {
  return request({
    url: '/matrix/evalManage',
    method: 'post',
    data: data
  })
}

// 修改评价管理
export function updateEvalManage(data) {
  return request({
    url: '/matrix/evalManage',
    method: 'put',
    data: data
  })
}

// 删除评价管理
export function delEvalManage(eid) {
  return request({
    url: '/matrix/evalManage/' + eid,
    method: 'delete'
  })
}

// 查询
export function getProInfo() {
  return request({
    url: '/matrix/evalManage/getProInfo',
    method: 'get'
  })
}
