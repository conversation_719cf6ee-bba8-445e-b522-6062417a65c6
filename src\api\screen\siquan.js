import request from '@/utils/request'

// 四全-水库信息
export function rsr_info() {
  return request({
    url: '/matrix/siquan/rsr_info',
    method: 'get',
  })
}

// 四全-水情信息
export function rsr_water() {
  return request({
    url: '/matrix/siquan/rsr_water',
    method: 'get',
  })
}

// 四全-雨情监测
export function rain_info() {
  return request({
    url: '/matrix/siquan/rain_info',
    method: 'get',
  })
}

//四全-工情监测-断面列表
export function listsections() {
  return request({
    url: '/matrix/osmometer/listsections',
    method: 'get',
  })
}

//四全-工情监测-渗压
export function osmometer_data(query) {
  return request({
    url: '/matrix/siquan/osmometer_data',
    method: 'get',
    params: query
  })
}

//四全-工情检测-位移
export function gnss_data(eid) {
  return request({
    url: '/matrix/siquan/gnss_data?eid=' + eid,
    method: 'get',
  })
}


//四全-水质监测
export function water_quality() {
  return request({
    url: '/matrix/siquan/water_quality',
    method: 'get',
  })
}

// 四全-流量监测
export function flow_info(query) {
  return request({
    url: '/matrix/siquan/flow_info',
    method: 'get',
    params: query
  })
}

//四全-全覆盖-注册登记信息
export function get_reg_info(encd) {
  return request({
    url: '/matrix/baseline/siquan/get_reg_info?encd=' + encd,
    method: 'get',
  })
}

//四全-全覆盖-水库概况
export function water_overview(encd) {
  return request({
    url: '/matrix/baseline/siquan/wrb_overview?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-库区要素
export function basin_info(encd) {
  return request({
    url: '/matrix/baseline/siquan/basin_info?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-水质检测
export function water_monit(encd) {
  return request({
    url: '/matrix/baseline/siquan/water_monit?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-年降雨量
export function year_avg_drp(encd) {
  return request({
    url: '/matrix/baseline/siquan/year_avg_drp?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-标识标牌
export function label_signs(encd) {
  return request({
    url: '/matrix/baseline/siquan/label_signs?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-机电设备
export function wrb_builder(encd) {
  return request({
    url: '/matrix/baseline/siquan/wrb_builder?encd=' + encd,
    method: 'get',
  })
}


//四全-全要素-库容曲线
export function capacity_curve(encd) {
  return request({
    url: '/matrix/baseline/siquan/capacity_curve?encd=' + encd,
    method: 'get',
  })
}

//根据水位查库容
export function rz_w_cure(rz, encd) {
  return request({
    url: '/matrix/baseline/siquan/rz_w_cure?rz=' + rz + '&encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-供蓄统计
export function wrb_statistics(encd) {
  return request({
    url: '/matrix/baseline/siquan/wrb_statistics?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-下游要素-淹没情况
export function downstream_info(encd) {
  return request({
    url: '/matrix/baseline/siquan/downstream_info?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-泄流曲线
export function gate_curve() {
  return request({
    url: '/matrix/baseline/siquan/gate_curve?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-机电设备
export function gate_status(encd) {
  return request({
    url: '/matrix/baseline/siquan/gate_status?encd=' + encd,
    method: 'get',
  })
}

//四全-全天候-水情信息
export function water_info(encd) {
  return request({
    url: '/matrix/baseline/siquan/water_info?encd=' + encd,
    method: 'get',
  })
}

//四全-全天候-雨情信息
export function base_rain_info(encd) {
  return request({
    url: '/matrix/baseline/siquan/rain_info?encd=' + encd,
    method: 'get',
  })
}

//四全-全天候-实测雨量
export function rain_list(stcd) {
  return request({
    url: '/matrix/baseline/siquan/rain_list?stcd=' + stcd,
    method: 'get',
  })
}

//四全-全天候-雨情信息二级弹窗
export function rain_base_data(query) {
  return request({
    url: '/matrix/baseline/siquan/rain_base_data',
    method: 'get',
    params: query
  })
}

//四全-全天候-断面信息
export function section_info(encd) {
  return request({
    url: '/matrix/baseline/siquan/section_info?encd=' + encd,
    method: 'get',
  })
}

//四全-全天候-位移监测
export function base_gnss_data(encd) {
  return request({
    url: '/matrix/baseline/siquan/gnss_data?encd=' + encd,
    method: 'get',
  })
}

//四全-全天候-渗压
export function base_osmometer_data(query) {
  return request({
    url: '/matrix/baseline/siquan/osmometer_data',
    method: 'get',
    params: query
  })
}

//四全-全周期-注册登记列表
export function register_login(encd) {
  return request({
    url: '/matrix/baseline/siquan/register_login?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-调度运用列表
export function schedule_run(encd) {
  return request({
    url: '/matrix/baseline/siquan/schedule_run?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-维修养护-年度维养计划
export function siquan_amc(year, encd) {
  return request({
    url: '/matrix/baseline/siquan/amc?year=' + year + '&encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-维修养护-日常维养计划
export function dmau(encd) {
  return request({
    url: '/matrix/baseline/siquan/dmau?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-维修养护-专项维养计划
export function maint(year, encd) {
  return request({
    url: '/matrix/baseline/siquan/maint?year=' + year + '&encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-检查监测
export function check_list(encd) {
  return request({
    url: '/matrix/baseline/siquan/check_list?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-安全鉴定列表
export function identification(encd) {
  return request({
    url: '/matrix/baseline/siquan/identification?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-除险加固列表
export function som_sm_repair(encd) {
  return request({
    url: '/matrix/baseline/siquan/som_sm_repair?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-应急管理-预案管理
export function som_sm_plan(encd) {
  return request({
    url: '/matrix/baseline/siquan/som_sm_plan?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-应急管理-物资库存
export function som_mgwh_mat(encd) {
  return request({
    url: '/matrix/baseline/siquan/som_mgwh_mat?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-应急管理-抢险队伍
export function som_mg_rescue_team(encd) {
  return request({
    url: '/matrix/baseline/siquan/som_mg_rescue_team?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-应急管理-险情管理
export function som_sm_danger(encd) {
  return request({
    url: '/matrix/baseline/siquan/som_sm_danger?encd=' + encd,
    method: 'get',
  })
}

//四全-全周期-大事记
export function cycle_record(encd) {
  return request({
    url: '/matrix/baseline/siquan/cycle_record?encd=' + encd,
    method: 'get',
  })
}

//四全-全要素-水质监测弹窗
export function pop_waterquality(query) {
  return request({
    url: '/matrix/baseline/siquan/pop_waterquality',
    method: 'get',
    params: query
  })
}

