import request from '@/utils/request'

// 查询水质评价依据列表
export function listBasis(query) {
  return request({
    url: '/matrix/basis/list',
    method: 'get',
    params: query
  })
}

// 查询水质评价依据详细
export function getBasis(wid) {
  return request({
    url: '/matrix/basis/' + wid,
    method: 'get'
  })
}

// 新增水质评价依据
export function addBasis(data) {
  return request({
    url: '/matrix/basis',
    method: 'post',
    data: data
  })
}

// 修改水质评价依据
export function updateBasis(data) {
  return request({
    url: '/matrix/basis',
    method: 'put',
    data: data
  })
}

// 删除水质评价依据
export function delBasis(wid) {
  return request({
    url: '/matrix/basis/' + wid,
    method: 'delete'
  })
}
