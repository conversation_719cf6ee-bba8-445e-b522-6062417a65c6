import request from '@/utils/request'

// 查询职能批复列表
export function listapproval(query) {
  return request({
    url: '/matrix/approval/list',
    method: 'get',
    params: query
  })
}

// 查询职能批复详细
export function getapproval(id) {
  return request({
    url: '/matrix/approval/detail/' + id,
    method: 'get'
  })
}

// 新增职能批复
export function addapproval(data) {
  return request({
    url: '/matrix/approval/add',
    method: 'post',
    data: data
  })
}

// 修改职能批复
export function updateapproval(data) {
  return request({
    url: '/matrix/approval/update',
    method: 'put',
    data: data
  })
}

// 删除职能批复
  export function delapproval(id) {
    return request({
      url: '/matrix/approval/delete/' + id,
      method: 'delete'
    })
}
