import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listProblem(query) {
    return request({
        url: '/rl/water/riverProblem/list',
        method: 'get',
        params: query
    })
}

// 查询【请填写功能名称】详细
export function getProblem(showXh) {
    return request({
        url: '/rl/water/riverProblem/' + showXh,
        method: 'get'
    })
}

// 新增【请填写功能名称】
export function addProblem(data) {
    return request({
        url: '/rl/water/riverProblem',
        method: 'post',
        data: data
    })
}

// 修改【请填写功能名称】
export function updateProblem(data) {
    return request({
        url: '/rl/water/riverProblem',
        method: 'put',
        data: data
    })
}

// 删除【请填写功能名称】
export function delProblem(showXh) {
    return request({
        url: '/rl/water/riverProblem/' + showXh,
        method: 'delete'
    })
}
