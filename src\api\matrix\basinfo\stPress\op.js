import request from '@/utils/request'

// 查询渗压测站列表
export function listOp(query) {
  return request({
    url: '/matrix/op/list',
    method: 'get',
    params: query
  })
}

// 查询渗压测站详细
export function getOp(rgid) {
  return request({
    url: '/matrix/op/' + rgid,
    method: 'get'
  })
}

// 新增渗压测站
export function addOp(data) {
  return request({
    url: '/matrix/op',
    method: 'post',
    data: data
  })
}

// 修改渗压测站
export function updateOp(data) {
  return request({
    url: '/matrix/op',
    method: 'put',
    data: data
  })
}

// 删除渗压测站
export function delOp(rgid) {
  return request({
    url: '/matrix/op/' + rgid,
    method: 'delete'
  })
}

export function Projlist() {
  return request({
    url: '/matrix/project/list1',
    method: 'get'
  })
}
export function  sectionlist(data) {
  return request({
    url: '/matrix/project/list2',
    method: 'get',
    params: data

  })
}
export function  allPoints() {
  return request({
    url: '/matrix/project/list3',
    method: 'get'
  })
}
