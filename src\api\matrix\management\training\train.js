import request from '@/utils/request'

// 查询培训管理列表
export function listTrain(query) {
  return request({
    url: '/matrix/train/list',
    method: 'get',
    params: query
  })
}

// 查询培训管理详细
export function getTrain(tid) {
  return request({
    url: '/matrix/train/' + tid,
    method: 'get'
  })
}

// 新增培训管理
export function addTrain(data) {
  return request({
    url: '/matrix/train',
    method: 'post',
    data: data
  })
}

// 修改培训管理
export function updateTrain(data) {
  return request({
    url: '/matrix/train',
    method: 'put',
    data: data
  })
}

// 删除培训管理
export function delTrain(tid) {
  return request({
    url: '/matrix/train/' + tid,
    method: 'delete'
  })
}

//培训列表
export function trainingList(data){
  return request({
    url: '/matrix/train/training_list',
    method: 'get',
    params: data
  })
}
//人员列表
export function listUser(){
  return request({
    url: '/matrix/train/listuser',
    method: 'get'
  })
}
