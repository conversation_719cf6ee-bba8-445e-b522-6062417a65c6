import request from '@/utils/request'

// 查询管理制度列表
export function listDoc(query) {
  return request({
    url: '/matrix/doc/list',
    method: 'get',
    params: query
  })
}

// 查询管理制度详细
export function getDoc(sid) {
  return request({
    url: '/matrix/doc/' + sid,
    method: 'get'
  })
}

// 新增管理制度
export function addDoc(data) {
  return request({
    url: '/matrix/doc',
    method: 'post',
    data: data
  })
}

// 修改管理制度
export function updateDoc(data) {
  return request({
    url: '/matrix/doc',
    method: 'put',
    data: data
  })
}

// 删除管理制度
export function delDoc(sid) {
  return request({
    url: '/matrix/doc/' + sid,
    method: 'delete'
  })
}
