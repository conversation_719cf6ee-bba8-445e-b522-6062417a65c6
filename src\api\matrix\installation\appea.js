import request from '@/utils/request'

// 查询形象面貌(水库)列表
export function listAppea(query) {
  return request({
    url: '/matrix/appea/list',
    method: 'get',
    params: query
  })
}

// 查询形象面貌(水库)详细
export function getAppea(aid) {
  return request({
    url: '/matrix/appea/' + aid,
    method: 'get'
  })
}

// 新增形象面貌(水库)
export function addAppea(data) {
  return request({
    url: '/matrix/appea',
    method: 'post',
    data: data
  })
}

// 修改形象面貌(水库)
export function updateAppea(data) {
  return request({
    url: '/matrix/appea',
    method: 'put',
    data: data
  })
}

// 删除形象面貌(水库)
export function delAppea(aid) {
  return request({
    url: '/matrix/appea/' + aid,
    method: 'delete'
  })
}
