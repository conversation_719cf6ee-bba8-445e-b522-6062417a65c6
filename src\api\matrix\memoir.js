import request from "@/utils/request";

// 查询大事记列表
export function listBigThing(query) {
  return request({
    url: "/matrix/bigThing/list",
    method: "get",
    params: query,
  });
}

// 查询大事记详细
export function getBigThing(id) {
  return request({
    url: "/matrix/bigThing/" + id,
    method: "get",
  });
}

// 新增大事记
export function addBigThing(data) {
  return request({
    url: "/matrix/bigThing/add",
    method: "post",
    data: data,
  });
}

// 修改大事记
export function updateBigThing(data) {
  return request({
    url: "/matrix/bigThing/update",
    method: "post",
    data: data,
  });
}

// 删除大事记
export function delBigThing(id) {
  return request({
    url: "/matrix/bigThing/" + id,
    method: "delete",
  });
}
