import request from '@/utils/request'

// 查询堤防管理列表
export function listDifang(query) {
  return request({
    url: '/product/water/difang/list',
    method: 'get',
    params: query
  })
}

// 查询堤防管理详细
export function getDifang(id) {
  return request({
    url: '/product/water/difang/' + id,
    method: 'get'
  })
}

// 新增堤防管理
export function addDifang(data) {
  return request({
    url: '/product/water/difang',
    method: 'post',
    data: data
  })
}

// 修改堤防管理
export function updateDifang(data) {
  return request({
    url: '/product/water/difang',
    method: 'put',
    data: data
  })
}

// 删除堤防管理
export function delDifang(id) {
  return request({
    url: '/product/water/difang/' + id,
    method: 'delete'
  })
}
