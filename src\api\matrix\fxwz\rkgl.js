import request from '@/utils/request'

// 查询物资入库列表
export function listWzStoreWh(query) {
  return request({
    url: '/matrix/wzStoreWh/list',
    method: 'get',
    params: query
  })
}

// 查询物资入库详细
export function getWzStoreWh(id) {
  return request({
    url: '/matrix/wzStoreWh/' + id,
    method: 'get'
  })
}

// 新增物资入库
export function addWzStoreWh(data) {
  return request({
    url: '/matrix/wzStoreWh',
    method: 'post',
    data: data
  })
}

// 修改物资入库
export function updateWzStoreWh(data) {
  return request({
    url: '/matrix/wzStoreWh',
    method: 'put',
    data: data
  })
}

// 删除物资入库
export function delWzStoreWh(id) {
  return request({
    url: '/matrix/wzStoreWh/' + id,
    method: 'delete'
  })
}
// 仓库列表
export function warehouseList(params) {
  return request({
    url: '/matrix/wzWarehouse/list',
    method: 'get',
    params
  })
}
// 查询某个仓库下的物资
export function listWzBinding(query) {
  return request({
    url: '/matrix/wzBinding/list',
    method: 'get',
    params: query
  })
}
