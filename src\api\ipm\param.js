import request from '@/utils/request'

// 查询区域绘制参数列表
export function listParam(query) {
  return request({
    url: '/ipm/param/list',
    method: 'get',
    params: query
  })
}

// 查询区域绘制参数详细
export function getParam(id) {
  return request({
    url: '/ipm/param/' + id,
    method: 'get'
  })
}

// 新增区域绘制参数
export function addParam(data) {
  return request({
    url: '/ipm/param',
    method: 'post',
    data: data
  })
}

// 修改区域绘制参数
export function updateParam(data) {
  return request({
    url: '/ipm/param',
    method: 'put',
    data: data
  })
}

// 删除区域绘制参数
export function delParam(id) {
  return request({
    url: '/ipm/param/' + id,
    method: 'delete'
  })
}
