import request from '@/utils/request'

//查询取水口信息
export function intake(query) {
    return request({
        url: '/rl/water/dynamic/intake/list',
        method: 'get',
        params: query
    })
}

//查询排污口信息
export function drain(query) {
    return request({
        url: '/rl/water/dynamic/drain/list',
        method: 'get',
        params: query
    })
}

//查询工程信息
export function engineer(query) {
    return request({
        url: '/rl/water/dynamic/engineer/list',
        method: 'get',
        params: query
    })
}

//查询堤防信息
export function dike(query) {
    return request({
        url: '/rl/water/dynamic/dike/list',
        method: 'get',
        params: query
    })
}


//查询其他建筑物
export function otherBuildList(query) {
    return request({
        url: '/rl/water/dynamic/otherBuild/list',
        method: 'get',
        params: query
    })
}

//查询饮用水水源地信息
export function waterSourceList(query) {
  return request({
    url: '/rl/water/dynamic/waterSourceList/list',
    method: 'get',
    params: query
  })
}

//查询河长信息
export function riverLength(query) {
    return request({
        url: '/rl/water/basic/river_length/list',
        method: 'get',
        params: query
    })
}

//查询河管员
export function riverCtrl(query) {
    return request({
        url: '/rl/water/basic/river_ctrl/list',
        method: 'get',
        params: query
    })
}

//查询监控信息
export function deviceInfo(query) {
    return request({
        url: '/rl/water/basic/device/list',
        method: 'get',
        params: query
    })
}

//查询水文站信息
export function riverStationInfo(query) {
    return request({
        url: '/rl/water/basic/river_station/list',
        method: 'get',
        params: query
    })
}

//查询河流支流信息
export function riverTributary(query) {
    return request({
        url: '/rl/water/basic/river_tributary/list',
        method: 'get',
        params: query
    })
}

//查询水功能区
export function riverFunction(query) {
    return request({
        url: '/rl/water/dynamic/river_function/list',
        method: 'get',
        params: query
    })
}

//查询水库信息
export function basRiverRes(query) {
    return request({
        url: '/rl/water/dynamic/basRiverRes/list',
        method: 'get',
        params: query
    })
}

//查询河流护岸护坡信息列表
export function basRiverRevetment(query) {
    return request({
        url: '/rl/water/dynamic/basRiverRevetment/list',
        method: 'get',
        params: query
    })
}

//查询河流自然文化资源列表
export function basRiverNatural(query) {
    return request({
        url: '/rl/water/dynamic/basRiverNatural/list',
        method: 'get',
        params: query
    })
}
