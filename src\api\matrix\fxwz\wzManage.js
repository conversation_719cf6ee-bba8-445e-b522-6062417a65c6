import request from '@/utils/request'

//仓库列表
export function getAllWhSelect() {
    return request({
      url: '/matrix/wh/whManage/getAllWhSelect',
      method: 'get',
    })
}
//根据仓库查询物资列表
export function getMatByWid(query) {
    return request({
      url: '/matrix/wh/mtManage/getMatByWid',
      method: 'get',
      params:query
    })
}

//物资管理
//列表
export function listWz(query) {
    return request({
      url: '/matrix/wh/mtManage/list',
      method: 'get',
      params: query
    })
}
//新增
export function wzAdd(data) {
    return request({
      url: '/matrix/wh/mtManage',
      method: 'post',
      data
    })
}

//查询单个详细信息
export function getWzDetail(did) {
    return request({
      url: '/matrix/wh/mtManage/' + did,
      method: 'get',
    })
}
//修改
export function updateWz(data) {
    return request({
      url: '/matrix/wh/mtManage',
      method: 'put',
      data
      
    })
}
// 删除
export function deleteWz(did) {
    return request({
      url: '/matrix/wh/mtManage/' + did,
      method: 'delete'
    })
}


// 根据物资获取明细
export function getMtByMid(query) {
    return request({
      url: '/matrix/wh/whManage/getMtByMid',
      method: 'get',
      params:query
    })
}

//出入库管理
// 查询物资明细列表
export function listDetail(query) {
  return request({
    url: '/matrix/wh/optManage/list',
    method: 'get',
    params: query
  })
}

//出入库弹窗列表
export function getDetailByMid(query) {
    return request({
      url: '/matrix/wh/optManage/getDetailByMid',
      method: 'get',
      params: query
    })
}
//出库
export function saveOut(data) {
    return request({
      url: '/matrix/wh/optManage/out',
      method: 'post',
      data
    })
}

//入库
export function saveIn(data) {
    return request({
      url: '/matrix/wh/optManage/in',
      method: 'post',
      data
    })
}

//人员列表
export function userList(query) {
    return request({
      url: '/system/user/list',
      method: 'get',
      params:query
    })
}



