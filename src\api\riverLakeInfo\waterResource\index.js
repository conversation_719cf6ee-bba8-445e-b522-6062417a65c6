import request from '@/utils/request'

// 查询饮用水水源地列表
export function listResource(query) {
  return request({
    url: '/product/IwaterResource/list',
    method: 'get',
    params: query
  })
}

// 查询饮用水水源地详细
export function getResource(id) {
  return request({
    url: '/product/IwaterResource/' + id,
    method: 'get'
  })
}

// 新增饮用水水源地
export function addResource(data) {
  return request({
    url: '/product/IwaterResource',
    method: 'post',
    data: data
  })
}

// 修改饮用水水源地
export function updateResource(data) {
  return request({
    url: '/product/IwaterResource',
    method: 'put',
    data: data
  })
}

// 删除饮用水水源地
export function delResource(id) {
  return request({
    url: '/product/IwaterResource/' + id,
    method: 'delete'
  })
}

// 查询所有正常使用的行政区域列表
export function listNormalArea() {
  return request({
      url: '/product/water/area/listArea',
      method: 'get',
  })
}