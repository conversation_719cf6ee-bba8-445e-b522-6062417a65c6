import request from '@/utils/request'

// 查询快照任务列表
export function listTask(query) {
  return request({
    url: '/ipm/task/list',
    method: 'get',
    params: query
  })
}

// 查询快照任务详细
export function getTask(id) {
  return request({
    url: '/ipm/task/' + id,
    method: 'get'
  })
}

// 新增快照任务
export function addTask(data) {
  return request({
    url: '/ipm/task',
    method: 'post',
    data: data
  })
}

// 修改快照任务
export function updateTask(data) {
  return request({
    url: '/ipm/task',
    method: 'put',
    data: data
  })
}

// 删除快照任务
export function delTask(id) {
  return request({
    url: '/ipm/task/' + id,
    method: 'delete'
  })
}

// 修改快照任务状态
export function changeTaskStatus(taskId, status) {
  const data = {
    id: taskId,
    enable: status
  }
  return request({
    url: '/ipm/task/change_status',
    method: 'put',
    data: data
  })
}
