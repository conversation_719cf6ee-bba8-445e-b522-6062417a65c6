import request from '@/utils/request'

// 查询组织机构职责列表
export function listDuty(query) {
  return request({
    url: '/matrix/org_duty/list',
    method: 'get',
    params: query
  })
}

// 查询组织机构职责详细
export function getDuty(did) {
  return request({
    url: '/matrix/org_duty/' + did,
    method: 'get'
  })
}

// 新增组织机构职责
export function addDuty(data) {
  return request({
    url: '/matrix/org_duty',
    method: 'post',
    data: data
  })
}

// 修改组织机构职责
export function updateDuty(data) {
  return request({
    url: '/matrix/org_duty',
    method: 'put',
    data: data
  })
}

// 删除组织机构职责
export function delDuty(did) {
  return request({
    url: '/matrix/org_duty/' + did,
    method: 'delete'
  })
}
