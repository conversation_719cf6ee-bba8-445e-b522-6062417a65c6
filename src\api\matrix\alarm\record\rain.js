import request from '@/utils/request'

// 查询雨情告警列表
export function listAlarm(query) {
  return request({
    url: '/matrix/rain_alarm/list',
    method: 'get',
    params: query
  })
}

// 查询雨情告警详细
export function getAlarm(sid) {
  return request({
    url: '/matrix/rain_alarm/' + sid,
    method: 'get'
  })
}

// 新增雨情告警
export function addAlarm(data) {
  return request({
    url: '/matrix/rain_alarm',
    method: 'post',
    data: data
  })
}

// 修改雨情告警
export function updateAlarm(data) {
  return request({
    url: '/matrix/rain_alarm',
    method: 'put',
    data: data
  })
}

// 删除雨情告警
export function delAlarm(sid) {
  return request({
    url: '/matrix/rain_alarm/' + sid,
    method: 'delete'
  })
}

// 手动解除告警
export function handleEdit(data) {
  return request({
    url: '/matrix/rain_alarm/edit',
    method: 'put',
    data: data
  })
}
