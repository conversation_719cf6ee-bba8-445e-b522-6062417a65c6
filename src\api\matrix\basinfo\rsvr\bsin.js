import request from '@/utils/request'

// 查询水库基本信息列表
export function listBsin(query) {
  return request({
    url: '/matrix/bsin/list',
    method: 'get',
    params: query
  })
}

// 查询水库基本信息详细
export function getBsin(bid) {
  return request({
    url: '/matrix/bsin/' + bid,
    method: 'get'
  })
}

// 新增水库基本信息
export function addBsin(data) {
  return request({
    url: '/matrix/bsin',
    method: 'post',
    data: data
  })
}

// 修改水库基本信息
export function updateBsin(data) {
  return request({
    url: '/matrix/bsin',
    method: 'put',
    data: data
  })
}

// 删除水库基本信息
export function delBsin(bid) {
  return request({
    url: '/matrix/bsin/' + bid,
    method: 'delete'
  })
}

//所在河流下拉框
export function basRiverSelect() {
  return request({
    url: '/matrix/section/basRiverSelect',
    method: 'get'
  })
}

//查询行政区划
export function getAreaSelect(query) {
  return request({
    url: '/matrix/section/areaSelect',
    method: 'get',
    params:query
  })
}
//根据value值查询行政区划名字
export function getAreaName(query) {
  return request({
    url: '/matrix/section/areaName',
    method: 'get',
    params: query
  })
}

//水库大坝 挡水建筑物
// 查询水库大坝列表
export function listDm(query) {
  return request({
    url: '/matrix/dm/list',
    method: 'get',
    params: query
  })
}

// 查询水库大坝详细
export function getDm(bid) {
  return request({
    url: '/matrix/dm/' + bid,
    method: 'get'
  })
}

// 新增水库大坝
export function addDm(data) {
  return request({
    url: '/matrix/dm',
    method: 'post',
    data: data
  })
}

// 修改水库大坝
export function updateDm(data) {
  return request({
    url: '/matrix/dm',
    method: 'put',
    data: data
  })
}

// 删除水库大坝
export function delDm(bid) {
  return request({
    url: '/matrix/dm/' + bid,
    method: 'delete'
  })
}

// 正常溢洪道
// 查询溢洪道列表
export function listYhd(query) {
  return request({
    url: '/matrix/yhd/list',
    method: 'get',
    params: query
  })
}

// 查询溢洪道详细
export function getYhd(bid) {
  return request({
    url: '/matrix/yhd/' + bid,
    method: 'get'
  })
}

// 新增溢洪道
export function addYhd(data) {
  return request({
    url: '/matrix/yhd',
    method: 'post',
    data: data
  })
}

// 修改溢洪道
export function updateYhd(data) {
  return request({
    url: '/matrix/yhd',
    method: 'put',
    data: data
  })
}

// 删除溢洪道
export function delYhd(bid) {
  return request({
    url: '/matrix/yhd/' + bid,
    method: 'delete'
  })
}

//非常溢洪道
// 查询非常溢洪道列表
export function listEmsw(query) {
  return request({
    url: '/matrix/emsw/list',
    method: 'get',
    params: query
  })
}

// 查询非常溢洪道详细
export function getEmsw(bid) {
  return request({
    url: '/matrix/emsw/' + bid,
    method: 'get'
  })
}

// 新增非常溢洪道
export function addEmsw(data) {
  return request({
    url: '/matrix/emsw',
    method: 'post',
    data: data
  })
}

// 修改非常溢洪道
export function updateEmsw(data) {
  return request({
    url: '/matrix/emsw',
    method: 'put',
    data: data
  })
}

// 删除非常溢洪道
export function delEmsw(bid) {
  return request({
    url: '/matrix/emsw/' + bid,
    method: 'delete'
  })
}

// 查询放水洞列表
export function listWtchtn(query) {
  return request({
    url: '/matrix/wtchtn/list',
    method: 'get',
    params: query
  })
}

// 查询放水洞详细
export function getWtchtn(bid) {
  return request({
    url: '/matrix/wtchtn/' + bid,
    method: 'get'
  })
}

// 新增放水洞
export function addWtchtn(data) {
  return request({
    url: '/matrix/wtchtn',
    method: 'post',
    data: data
  })
}

// 修改放水洞
export function updateWtchtn(data) {
  return request({
    url: '/matrix/wtchtn',
    method: 'put',
    data: data
  })
}

// 删除放水洞
export function delWtchtn(bid) {
  return request({
    url: '/matrix/wtchtn/' + bid,
    method: 'delete'
  })
}

// 水文特征
// 查询水库水文特征列表
export function listHych(query) {
  return request({
    url: '/matrix/hych/list',
    method: 'get',
    params: query
  })
}

// 查询水库水文特征详细
export function getHych(bid) {
  return request({
    url: '/matrix/hych/' + bid,
    method: 'get'
  })
}

// 新增水库水文特征
export function addHych(data) {
  return request({
    url: '/matrix/hych',
    method: 'post',
    data: data
  })
}

// 修改水库水文特征
export function updateHych(data) {
  return request({
    url: '/matrix/hych',
    method: 'put',
    data: data
  })
}

// 删除水库水文特征
export function delHych(bid) {
  return request({
    url: '/matrix/hych/' + bid,
    method: 'delete'
  })
}




//水库水闸
// 查询水库闸门列表
export function listGate(query) {
  return request({
    url: '/matrix/gate/list',
    method: 'get',
    params: query
  })
}

// 查询水库闸门详细
export function getGate(gid) {
  return request({
    url: '/matrix/gate/gid/' + gid,
    method: 'get'
  })
}

// 新增水库闸门
export function addGate(data) {
  return request({
    url: '/matrix/gate',
    method: 'post',
    data: data
  })
}

// 修改水库闸门
export function updateGate(data) {
  return request({
    url: '/matrix/gate',
    method: 'put',
    data: data
  })
}

// 删除水库闸门
export function delGate(gid) {
  return request({
    url: '/matrix/gate/' + gid,
    method: 'delete'
  })
}

//工程断面
// 查询水库断面列表
export function listRsp(query) {
  return request({
    url: '/matrix/rsp/list',
    method: 'get',
    params: query
  })
}

// 查询水库断面详细
export function getRsp(eid) {
  return request({
    url: '/matrix/rsp/' + eid,
    method: 'get'
  })
}

// 查询水库断面详细
export function getRspByBid(bid) {
  return request({
    url: '/matrix/rsp/info/' + bid,
    method: 'get'
  })
}

// 新增水库断面
export function addRsp(data) {
  return request({
    url: '/matrix/rsp',
    method: 'post',
    data: data
  })
}

// 修改水库断面
export function updateRsp(data) {
  return request({
    url: '/matrix/rsp',
    method: 'put',
    data: data
  })
}

// 删除水库断面
export function delRsp(eid) {
  return request({
    url: '/matrix/rsp/' + eid,
    method: 'delete'
  })
}

//责任人

// 查询工程责任人列表
export function listPerson(query) {
  return request({
    url: 'matrix/person/list',
    method:'get',
    params: query
  })
}

//  查询工程责任人详细
export function getPerson(pid) {
  return request({
    url: 'matrix/person/pid/' + pid,
    method: 'get'
  })
}

//  新增工程责任人
export function addPerson(data) {
  return request({
    url:'matrix/person',
    method: 'post',
    data: data
  })
}

//  修改工程责任人
export function updatePerson(data) {
  return request({
    url: 'matrix/person',
    method: 'put',
    data: data
  })
}

// 删除工程责任人
export function delPerson(pid) {
  return request({
    url: 'matrix/person/' + pid,
    method: 'delete'
  })
}
// 查询测站 waterdetection/list
