import request from '@/utils/request'
//获取抓图任务
export function taskList(query) {
  return request({
    url: '/ipm/task/list',
    method: 'get',
    params: query
  })
}
//获取区域绘制所有可用配置项信息
export function paramTree(query) {
  return request({
    url: '/ipm/param/tree',
    method: 'get',
    params: query
  })
}
//获取抓图任务详细信息
export function ipmTaskById(query) {
  return request({
    url: '/ipm/task/'+query,
    method: 'get',
  })
}
//获取抓图任务详细信息
export function editTask(data) {
  return request({
    url: '/ipm/task',
    method: 'put',
    data:data
  })
}
//更新抓图任务的区域绘制信息
export function regionsTask(data) {
  return request({
    url: '/ipm/task/regions',
    method: 'put',
    data:data
  })
}
//更新抓图任务的区域绘制信息
export function getRegionsTask(data) {
  return request({
    url: '/ipm/task/regions/'+data,
    method: 'get',
  })
}