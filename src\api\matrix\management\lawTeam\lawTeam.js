import request from '@/utils/request'

// 查询执法队伍列表
export function listlawTeam(query) {
  return request({
    url: '/matrix/lawTeam/list',
    method: 'get',
    params: query
  })
}

// 查询执法队伍详细
export function getlawTeam(id) {
  return request({
    url: '/matrix/lawTeam/detail/' + id,
    method: 'get'
  })
}

// 新增执法队伍
export function addlawTeam(data) {
  return request({
    url: '/matrix/lawTeam/add',
    method: 'post',
    data: data
  })
}

// 修改执法队伍
export function updatelawTeam(data) {
  return request({
    url: '/matrix/lawTeam/update',
    method: 'put',
    data: data
  })
}

// 删除执法队伍
export function dellawTeam(id) {
    return request({
      url: '/matrix/lawTeam/delete/' + id,
      method: 'delete'
    })
}
