<krpano version="1.19" title="ZHY">

	<include url="skin/vtourskin.xml" />
<include url="plugins/callout.xml"/>
	<!-- customize skin settings: maps, gyro, webvr, thumbnails, tooltips, layout, design, ... -->
	<skin_settings maps="false"
	               maps_type="google"
	               maps_bing_api_key=""
	               maps_google_api_key=""
	               maps_zoombuttons="false"
	               gyro="true"
	               webvr="true"
	               webvr_gyro_keeplookingdirection="false"
	               webvr_prev_next_hotspots="true"
	               littleplanetintro="true"
	               title="true"
	               thumbs="true"
	               thumbs_width="120" thumbs_height="80" thumbs_padding="10" thumbs_crop="0|40|240|160"
	               thumbs_opened="false"
	               thumbs_text="false"
	               thumbs_dragging="true"
	               thumbs_onhoverscrolling="false"
	               thumbs_scrollbuttons="false"
	               thumbs_scrollindicator="false"
	               thumbs_loop="false"
	               tooltips_buttons="false"
	               tooltips_thumbs="false"
	               tooltips_hotspots="false"
	               tooltips_mapspots="false"
	               deeplinking="false"
	               loadscene_flags="MERGE"
	               loadscene_blend="OPENBLEND(0.5, 0.0, 0.75, 0.05, linear)"
	               loadscene_blend_prev="SLIDEBLEND(0.5, 180, 0.75, linear)"
	               loadscene_blend_next="SLIDEBLEND(0.5,   0, 0.75, linear)"
	               loadingtext="loading..."
	               layout_width="100%"
	               layout_maxwidth="814"
	               controlbar_width="-24"
	               controlbar_height="40"
	               controlbar_offset="20"
	               controlbar_offset_closed="-40"
	               controlbar_overlap.no-fractionalscaling="10"
	               controlbar_overlap.fractionalscaling="0"
	               design_skin_images="vtourskin.png"
	               design_bgcolor="0x2D3E50"
	               design_bgalpha="0.8"
	               design_bgborder="0"
	               design_bgroundedge="1"
	               design_bgshadow="0 4 10 0x000000 0.3"
	               design_thumbborder_bgborder="3 0xFFFFFF 1.0"
	               design_thumbborder_padding="2"
	               design_thumbborder_bgroundedge="0"
	               design_text_css="color:#FFFFFF; font-family:Arial;"
	               design_text_shadow="1"
	               />

	<!--
	    For an alternative skin design either change the <skin_settings> values 
	    from above or optionally include one of the predefined designs from below.
	-->
	<!-- <include url="skin/vtourskin_design_flat_light.xml"  /> -->
	<!-- <include url="skin/vtourskin_design_glass.xml"       /> -->
	<!-- <include url="skin/vtourskin_design_ultra_light.xml" /> -->
	<!-- <include url="skin/vtourskin_design_117.xml"         /> -->
	<!-- <include url="skin/vtourskin_design_117round.xml"    /> -->
	<!-- <include url="skin/vtourskin_design_black.xml"       /> -->


	<!-- startup action - load the first scene -->
	<action name="startup" autorun="onstart">
		if(startscene === null OR !scene[get(startscene)], copy(startscene,scene[0].name); );
		loadscene(get(startscene), null, MERGE);
		if(startactions !== null, startactions() );
	</action>

<action name="hide_all_the_time_tooltip_for_VR">
	
	 txtadd(tooltipname, 'vrtooltip_', get(name));
	 addhotspot(get(tooltipname)); 
	 set(hotspot[get(tooltipname)],
		type='',
		edge=get(hotspot[get(name)].edge),
		distorted=get(hotspot[get(name)].distorted),
		ath=get(hotspot[get(name)].ath),
		atv=get(hotspot[get(name)].atv),
		oy=-30,
		ox=0,
		vcenter=true,
		padding=5,
                    alpha='0',
		mipmapping=true,
		oversampling=2,
		bg=true,
		bgcolor=0x000000,
		bgroundedge=5,
		bgalpha=0.65,
		bgborder=0,
		bgshadow='0 0 0 0x000000 0',
		css=calc(device.mobile ? 'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-weight:bold; font-size:14px;':'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-size:14px;'),
		txtshadow='0 0 0 0x000000 0';
		enabled=true,
        );
         txtadd(hotspot[get(tooltipname)].onclick,'callwith(hotspot[',get(name),'],onclick)');  
	 if(text == '' OR text === null,
 
		 copy(hotspot[get(tooltipname)].html,scene[get(linkedscene)].title),
		 copy(hotspot[get(tooltipname)].html,text);
 
	 ); 
	 if(lp_running == false,
	 	set(hotspot[get(tooltipname)].visible,true); 
	 	, 	
	 	if(!webvr.isenabled,
	 	  if(lp_running == true,
		 	set(hotspot[get(tooltipname)].visible,false); 
		 	set(hotspot[get(tooltipname)].mark2,true);
	 	   );
	 	  );
	 	);
	 if(hotspot[get(name)].normal == false, 
 
		set(hotspot[get(tooltipname)].normal,false);
		set(hotspot[get(tooltipname)].onloaded,
			if(webvr.isenabled,
				set(visible,false);
				,	
				if(lp_running == false OR lp_running == null OR lp_running === null,  
					set(visible,true);
					);
				);
	       );
 
	 	);
</action>

<action name="add_all_the_time_tooltip_for_VR">
	
	 txtadd(tooltipname, 'vrtooltip_', get(name));
	 addhotspot(get(tooltipname)); 
	 set(hotspot[get(tooltipname)],
		type=text,
		edge=get(hotspot[get(name)].edge),
		distorted=get(hotspot[get(name)].distorted),
		ath=get(hotspot[get(name)].ath),
		atv=get(hotspot[get(name)].atv),
		oy=-30,
		ox=0,
		vcenter=true,
		padding=5,
		mipmapping=true,
		oversampling=2,
		bg=true,
		bgcolor=0x000000,
		bgroundedge=5,
		bgalpha=0.65,
                    alpha='1',
		bgborder=0,
		bgshadow='0 0 0 0x000000 0',
		css=calc(device.mobile ? 'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-weight:bold; font-size:14px;':'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-size:14px;'),
		txtshadow='0 0 0 0x000000 0';
		enabled=true,
        );
         txtadd(hotspot[get(tooltipname)].onclick,'callwith(hotspot[',get(name),'],onclick)');  
	 if(text == '' OR text === null,
 
		 copy(hotspot[get(tooltipname)].html,scene[get(linkedscene)].title),
		 copy(hotspot[get(tooltipname)].html,text);
 
	 ); 
	 if(lp_running == false,
	 	set(hotspot[get(tooltipname)].visible,true); 
	 	, 	
	 	if(!webvr.isenabled,
	 	  if(lp_running == true,
		 	set(hotspot[get(tooltipname)].visible,false); 
		 	set(hotspot[get(tooltipname)].mark2,true);
	 	   );
	 	  );
	 	);
	 if(hotspot[get(name)].normal == false, 
 
		set(hotspot[get(tooltipname)].normal,false);
		set(hotspot[get(tooltipname)].onloaded,
			if(webvr.isenabled,
				set(visible,false);
				,	
				if(lp_running == false OR lp_running == null OR lp_running === null,  
					set(visible,true);
					);
				);
	       );
 
	 	);
</action>

<action name="goSpot">
           txtadd(oldtooltipname, 'vrtooltip_', get(name));
	 removehotspot(get(oldtooltipname)); 


           
	 txtadd(tooltipname, 'newvrtooltip_', get(name));
	 addhotspot(get(tooltipname)); 

 if(name=="spot2",
               set(hotspot[get(name)].text,"设备简介[br]雨雪量计是利用加热、不冻[br]液等方式将固态降水（雪、[br]雨夹雪）融化为液态后，进[br]行雨雪量自动测量的仪器。[br]融雪型雨雪量计由融雪装[br]置、雨量传感器、记录器三[br]部分组成。");
set(hotspot[get(tooltipname)].oy,"-170");               
);
if(name=="spot3",
               set(hotspot[get(name)].text,"设备简介[br]翻斗式雨量计是一种水文、[br]气象仪器，用以测量自然界[br]降雨量，同时将降雨量转换[br]为以开关量形式表示的数字[br]信息量输出，以满足信息传[br]输、处理、记录和显示等[br]需要。");
set(hotspot[get(tooltipname)].oy,"-170");           
    );
if(name=="spot4",
               set(hotspot[get(name)].text,"设备简介[br]普通雨量计是一种水文、[br]气象仪器，用以测量自然界[br]降雨量，同时将降雨量转换[br]为以开关量形式表示的数字[br]信息量输出，以满足信息传[br]输、处理、记录和显示等[br]需要。");
set(hotspot[get(tooltipname)].oy,"-170");          
     );
if(name=="spot5",
               set(hotspot[get(name)].text,"设备简介[br]使用高精度、温度漂移小[br]的硅电容压力传感器作为压力[br]感知元件，能够满足地下水[br]监测严格的要求，集水位、[br]水温监测于一体，专注于各[br]种场景的地下水情监测。");
set(hotspot[get(tooltipname)].oy,"-160");       
        );
if(name=="spot6",
               set(hotspot[get(name)].text,"设备简介[br]土壤水分测量仪是一款以介[br]电常数检测原理为基础的传[br]感器。能够针对不同土层的[br]土壤水分含量进行动态观[br]测，而且是进行快速、准[br]确、全面地观测，让人实[br]现对土壤的高度感知。");
set(hotspot[get(tooltipname)].oy,"-170");          
     );
if(name=="spot7",
               set(hotspot[get(name)].text,"设备简介[br]20cm蒸发器是用于测定大气[br]中一定时间间隔内水的蒸发[br]量的仪器。");
set(hotspot[get(tooltipname)].oy,"-120");            
   );

	 set(hotspot[get(tooltipname)],
		type=text,
		edge=get(hotspot[get(name)].edge),
		distorted=get(hotspot[get(name)].distorted),
		ath=get(hotspot[get(name)].ath),
		atv=get(hotspot[get(name)].atv),
		
		ox=0,
		vcenter=true,
		padding=5,
		mipmapping=true,
		oversampling=2,
		bg=true,
		bgcolor=0x000000,
		bgroundedge=5,
		bgalpha=0.65,
                    alpha='1',
		bgborder=0,
		bgshadow='0 0 0 0x000000 0',
		css=calc(device.mobile ? 'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-weight:bold; font-size:14px;':'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-size:14px;'),
		txtshadow='0 0 0 0x000000 0';
		enabled=true,
        );
   
         txtadd(hotspot[get(tooltipname)].onclick,'callwith(hotspot[',get(name),'],onclick)');  
	 if(text == '' OR text === null,
 
		 copy(hotspot[get(tooltipname)].html,scene[get(linkedscene)].title),
		 copy(hotspot[get(tooltipname)].html,text);
 
	 ); 
	 if(lp_running == false,
	 	set(hotspot[get(tooltipname)].visible,true); 
	 	, 	
	 	if(!webvr.isenabled,
	 	  if(lp_running == true,
		 	set(hotspot[get(tooltipname)].visible,false); 
		 	set(hotspot[get(tooltipname)].mark2,true);
	 	   );
	 	  );
	 	);
	 if(hotspot[get(name)].normal == false, 
 
		set(hotspot[get(tooltipname)].normal,false);
		set(hotspot[get(tooltipname)].onloaded,
			if(webvr.isenabled,
				set(visible,false);
				,	
				if(lp_running == false OR lp_running == null OR lp_running === null,  
					set(visible,true);
					);
				);
	       );
 
	 	);
</action>
<action name="backSpot">
           txtadd(oldtooltipname, 'vrtooltip_', get(name));
	 removehotspot(get(oldtooltipname)); 
           
 if(name=="spot2",
               set(hotspot[get(name)].text,"雨雪量计[br]ZLYX-200");
               );
if(name=="spot3",
               set(hotspot[get(name)].text,"翻斗式雨量计[br]ZDJ05-1");
               );
if(name=="spot4",
               set(hotspot[get(name)].text,"普通雨量器[br]JQH-1");
               );
if(name=="spot5",
               set(hotspot[get(name)].text,"地下水遥测水位计[br]HR.WYS-111");
               );
if(name=="spot6",
               set(hotspot[get(name)].text,"土壤分析仪[br]QY-800S");
               );
if(name=="spot7",
               set(hotspot[get(name)].text,"20cm蒸发器[br]JY-AM3");
               );

	 txtadd(tooltipname, 'newvrtooltip_', get(name));
	 addhotspot(get(tooltipname)); 
	 set(hotspot[get(tooltipname)],
		type=text,
		edge=get(hotspot[get(name)].edge),
		distorted=get(hotspot[get(name)].distorted),
		ath=get(hotspot[get(name)].ath),
		atv=get(hotspot[get(name)].atv),
		oy=-50,
		ox=0,
		vcenter=true,
		padding=5,
		mipmapping=true,
		oversampling=2,
		bg=true,
		bgcolor=0x000000,
		bgroundedge=5,
		bgalpha=0.65,
                    alpha='1',
		bgborder=0,
		bgshadow='0 0 0 0x000000 0',
		css=calc(device.mobile ? 'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-weight:bold; font-size:14px;':'text-align:center; color:#FFFFFF; font-family:MicrosoftYahei; font-size:14px;'),
		txtshadow='0 0 0 0x000000 0';
		enabled=true,
        );
   
         txtadd(hotspot[get(tooltipname)].onclick,'callwith(hotspot[',get(name),'],onclick)');  
	 if(text == '' OR text === null,
 
		 copy(hotspot[get(tooltipname)].html,scene[get(linkedscene)].title),
		 copy(hotspot[get(tooltipname)].html,text);
 
	 ); 
	 if(lp_running == false,
	 	set(hotspot[get(tooltipname)].visible,true); 
	 	, 	
	 	if(!webvr.isenabled,
	 	  if(lp_running == true,
		 	set(hotspot[get(tooltipname)].visible,false); 
		 	set(hotspot[get(tooltipname)].mark2,true);
	 	   );
	 	  );
	 	);
	 if(hotspot[get(name)].normal == false, 
 
		set(hotspot[get(tooltipname)].normal,false);
		set(hotspot[get(tooltipname)].onloaded,
			if(webvr.isenabled,
				set(visible,false);
				,	
				if(lp_running == false OR lp_running == null OR lp_running === null,  
					set(visible,true);
					);
				);
	       );
 
	 	);
</action>
	
	<scene name="scene_0001" title="库中央鸟瞰" onstart="" havevrimage="true" thumburl="panos/0001.tiles/thumb.jpg" lat="35.44469297" lng="117.96921194" heading="0.0">

		<view hlookat="15.020" vlookat="13.508" fovtype="MFOV" fov="114.866" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0001.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0001.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0001.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0001.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0001.tiles/vr/pano_%s.jpg" />
		</image>
 <action name="normalview"  >
   copy(lp_scene, xml.scene);
    copy(lp_hlookat, view.hlookat);
    copy(lp_vlookat, view.vlookat);
    copy(lp_fov, view.fov);
    copy(lp_fovmax, view.fovmax);
    copy(lp_limitview, view.limitview);
    set(view.limitview, lookto);
set(view, hlookat=calc(xml.view.hlookat+180), vlookat=90.0, fisheye=1.0, fov=150.0);
    lookat(calc(lp_hlookat - 180), 90, 150, 1, 0, 0);
    wait(BLEND);
    delayedcall(0.5,

    set(control.usercontrol, off);
    copy(view.limitview, lp_limitview);
    set(view.vlookatmin, null);
    set(view.vlookatmax, null);
    tween(view.hlookat|view.vlookat|view.fov|view.distortion, calc('' + lp_hlookat + '|' + lp_vlookat + '|' + lp_fov + '|' + 0.0),
    3.0, easeOutQuad,
    set(control.usercontrol, all);
    tween(view.fovmax, get(lp_fovmax));

    
    );
    );
  </action>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="6.844" atv="8.461" linkedscene="scene_0002" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="29.154" atv="10.123" linkedscene="scene_0004" />
<hotspot name="hs1" ath="67.858954" atv="18.803283"  style="callout"
                       title="费县大田庄东方国际酒店"
                    />
	</scene>

	<scene name="scene_0002" title="坝前坡" onstart="" havevrimage="true" thumburl="panos/0002.tiles/thumb.jpg" lat="35.43259447" lng="117.96987114" heading="0.0">

		<view hlookat="-134.512" vlookat="19.525" fovtype="MFOV" fov="120.000" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0002.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0002.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0002.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0002.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0002.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="-150.151" atv="7.365" linkedscene="scene_0005" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="-135.419" atv="7.770" linkedscene="scene_0004" />
		<hotspot name="spot3" style="skin_hotspotstyle" ath="88.898" atv="17.093" linkedscene="scene_0003" />
		<hotspot name="spot4" style="skin_hotspotstyle" ath="-81.937" atv="-10.604" linkedscene="scene_0001" />

	</scene>

	<scene name="scene_0003" title="坝后坡" onstart="" havevrimage="true" thumburl="panos/0003.tiles/thumb.jpg" lat="35.43106997" lng="117.97177136" heading="0.0">

		<view hlookat="1.179" vlookat="4.323" fovtype="MFOV" fov="120.000" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0003.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0003.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0003.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0003.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0003.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="1.179" atv="-7.305" linkedscene="scene_0001" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="-2.009" atv="13.524" linkedscene="scene_0002" />
		<hotspot name="spot3" style="skin_hotspotstyle" ath="-46.430" atv="4.097" linkedscene="scene_0005" />
		<hotspot name="spot4" style="skin_hotspotstyle" ath="-36.963" atv="4.437" linkedscene="scene_0004" />

	</scene>

	<scene name="scene_0004" title="溢洪闸前" onstart="" havevrimage="true" thumburl="panos/0004.tiles/thumb.jpg" lat="35.43419569" lng="117.96514358" heading="0.0">

		<view hlookat="3.014" vlookat="45.000" fovtype="MFOV" fov="129.403" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0004.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0004.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0004.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0004.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0004.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="-147.687" atv="-3.475" linkedscene="scene_0001" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="-83.796" atv="6.310" linkedscene="scene_0002" />
		<hotspot name="spot3" style="skin_hotspotstyle" ath="-65.678" atv="6.132" linkedscene="scene_0003" />
		<hotspot name="spot4" style="skin_hotspotstyle" ath="-1.416" atv="25.842" linkedscene="scene_0005" />
<hotspot name="hs1" ath="6.314482" atv="55.602429"  style="callout"
                       title="溢洪闸"
                    />
	</scene>

	<scene name="scene_0005" title="溢洪闸后" onstart="" havevrimage="true" thumburl="panos/0005.tiles/thumb.jpg" lat="35.43306722" lng="117.96467494" heading="0.0">

		<view hlookat="-5.004" vlookat="26.044" fovtype="MFOV" fov="120.000" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0005.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0005.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0005.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0005.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0005.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="-1.042" atv="17.131" linkedscene="scene_0004" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="15.911" atv="-2.950" linkedscene="scene_0001" />
		<hotspot name="spot3" style="skin_hotspotstyle" ath="72.974" atv="7.001" linkedscene="scene_0002" />
		<hotspot name="spot4" style="skin_hotspotstyle" ath="110.519" atv="9.828" linkedscene="scene_0003" />
		<hotspot name="spot5" style="skin_hotspotstyle" ath="138.047" atv="5.215" linkedscene="scene_0006" />

<hotspot name="hs1" ath="20.414507" atv="21.113806"  style="callout"
                       title="上冶水库管理处"
                    />
	</scene>

	<scene name="scene_0006" title="东岭水文站" onstart="" havevrimage="true" thumburl="panos/0006.tiles/thumb.jpg" lat="35.39869858" lng="117.96096531" heading="0.0">

		<view hlookat="46.757" vlookat="37.059" fovtype="MFOV" fov="133.645" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0006.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0006.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0006.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0006.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0006.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="-0.049" atv="5.393" linkedscene="scene_0007" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="-173.747" atv="-0.763" linkedscene="scene_0005" />
<hotspot name="hs1" ath="78.680817" atv="45.744826"  style="callout"
                       title="东岭水文站"
                    />
	</scene>

	<scene name="scene_0007" title="橡胶坝" onstart="" havevrimage="true" thumburl="panos/0007.tiles/thumb.jpg" lat="35.39438011" lng="117.96157456" heading="0.0">

		<view hlookat="186.783" vlookat="3.795" fovtype="MFOV" fov="120.000" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0007.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0007.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0007.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0007.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0007.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="179.748" atv="0.561" linkedscene="scene_0008" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="-12.719" atv="6.079" linkedscene="scene_0006" />
<hotspot name="hs2" ath="-167.237436" atv="25.391567" style="callout" rotate="300" callout_bg_border="0,2,0,0" text_padding="10 0 0 0"
                        title="东岭橡胶坝"
                     />
	</scene>

	<scene name="scene_0008" title="汇流口一" onstart="" havevrimage="true" thumburl="panos/0008.tiles/thumb.jpg" lat="35.37969622" lng="117.96088303" heading="0.0">

		<view hlookat="11.416" vlookat="11.334" fovtype="MFOV" fov="120.000" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0008.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0008.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0008.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0008.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0008.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="19.063" atv="1.316" linkedscene="scene_0009" />
		<hotspot name="spot2" style="skin_hotspotstyle" ath="-130.260" atv="2.414" linkedscene="scene_0007" />

	</scene>

	<scene name="scene_0009" title="汇流口二" onstart="" havevrimage="true" thumburl="panos/0009.tiles/thumb.jpg" lat="35.36060989" lng="117.97061114" heading="0.0">

		<view hlookat="433.338" vlookat="16.959" fovtype="MFOV" fov="129.403" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0009.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0009.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0009.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0009.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0009.tiles/vr/pano_%s.jpg" />
		</image>
		<hotspot name="spot1" style="skin_hotspotstyle" ath="88.655" atv="0.984" linkedscene="scene_0008" />

	</scene>
<callout 
	key="your license key" 
	size="10" 
	color="0xffffff"
	rotate="45"
	distancepopup="5"   
	min_fov="180" 
	max_fov="0" 
	line_lenght="100"
	line_width="2" 
	shape="round"
	circle_active_width="50"
	animation="center_screen" 
	text_width="300"
	callout_bg_border="0,0,0,2"
	text_x="10"
	css_title="color:#fff;font-size:25px;"
	css_text="color:#fff;font-size:16px;text-shadow: 3px 3px 8px #000000;"
	title_background="0xb40000"
	text_background=""
	title_padding="10"
	text_padding="10 10 0 0"
/>

</krpano>
