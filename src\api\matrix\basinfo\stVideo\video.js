import request from '@/utils/request'

// 查询视频测站列表
export function listVideo(query) {
  return request({
    url: '/matrix/video/list',
    method: 'get',
    params: query
  })
}

// 查询视频测站详细
export function getVideo(vid) {
  return request({
    url: '/matrix/video/' + vid,
    method: 'get'
  })
}

// 新增视频测站
export function addVideo(data) {
  return request({
    url: '/matrix/video',
    method: 'post',
    data: data
  })
}

// 修改视频测站
export function updateVideo(data) {
  return request({
    url: '/matrix/video',
    method: 'put',
    data: data
  })
}

// 删除视频测站
export function delVideo(vid) {
  return request({
    url: '/matrix/video/' + vid,
    method: 'delete'
  })
}
export function Projlist() {
  return request({
    url: '/matrix/project/list1',
    method: 'get'
  })
}

