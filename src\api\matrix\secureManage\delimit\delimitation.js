import request from '@/utils/request'

// 查询划界确权列表
export function listDelimitation(query) {
  return request({
    url: '/matrix/delimitation/list',
    method: 'get',
    params: query
  })
}

//根据type查询工程
export function getEnByType(type) {
    return request({
      url: "/matrix/registration/getEnByType",
      method: "get",
      params: type
    })
  }

// 查询划界确权详细
export function getDelimitation(did) {
  return request({
    url: '/matrix/delimitation/' + did,
    method: 'get'
  })
}

// 新增划界确权
export function addDelimitation(data) {
  return request({
    url: '/matrix/delimitation',
    method: 'post',
    data: data
  })
}

// 修改划界确权
export function updateDelimitation(data) {
  return request({
    url: '/matrix/delimitation',
    method: 'put',
    data: data
  })
}

// 删除划界确权
export function delDelimitation(did) {
  return request({
    url: '/matrix/delimitation/' + did,
    method: 'delete'
  })
}

//导出划界确权
export function delimitExport(data) {
    return request({
        url: "/matrix/delimitation/export",
        method: "post",
        data: data
    })
}