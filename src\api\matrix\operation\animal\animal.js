import request from '@/utils/request'

// 查询害堤动物列表
export function listAnimal(query) {
  return request({
    url: '/matrix/animal/list',
    method: 'get',
    params: query
  })
}

// 查询害堤动物详细
export function getAnimal(did) {
  return request({
    url: '/matrix/animal/' + did,
    method: 'get'
  })
}

// 新增害堤动物
export function addAnimal(data) {
  return request({
    url: '/matrix/animal',
    method: 'post',
    data: data
  })
}

// 修改害堤动物
export function updateAnimal(data) {
  return request({
    url: '/matrix/animal',
    method: 'put',
    data: data
  })
}

// 删除害堤动物
export function delAnimal(did) {
  return request({
    url: '/matrix/animal/' + did,
    method: 'delete'
  })
}
