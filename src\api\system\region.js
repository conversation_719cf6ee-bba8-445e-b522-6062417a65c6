import request from '@/utils/request'

// 查询行政区划列表
export function listRegion(query) {
  return request({
    url: '/system/region/list',
    method: 'get',
    params: query
  })
}

// 查询行政区划详细
export function getRegion(regionId) {
  return request({
    url: '/system/region/' + regionId,
    method: 'get'
  })
}

// 新增行政区划
export function addRegion(data) {
  return request({
    url: '/system/region',
    method: 'post',
    data: data
  })
}

// 修改行政区划
export function updateRegion(data) {
  return request({
    url: '/system/region',
    method: 'put',
    data: data
  })
}

// 删除行政区划
export function delRegion(regionId) {
  return request({
    url: '/system/region/' + regionId,
    method: 'delete'
  })
}

// 查询区划列表（排除节点）
export function listRegionExcludeChild(regionId) {
  return request({
    url: '/system/region/list/exclude/' + regionId,
    method: 'get'
  })
}

// 获取行政区划树
export function getRegionTree(query) {
  return request({
    url: "/system/region/regionTree",
    method: "get",
    params: query
  })
}
