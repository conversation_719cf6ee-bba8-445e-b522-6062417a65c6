import request from '@/utils/request'

// 查询水质评价依据标准表列表
export function listRule(query) {
  return request({
    url: '/matrix/rule/list',
    method: 'get',
    params: query
  })
}

// 查询水质评价依据标准表详细
export function getRule(evaluaBasis) {
  return request({
    url: '/matrix/rule/' + evaluaBasis,
    method: 'get'
  })
}

// 新增水质评价依据标准表
export function addRule(data) {
  return request({
    url: '/matrix/rule',
    method: 'post',
    data: data
  })
}

// 修改水质评价依据标准表
export function updateRule(data) {
  return request({
    url: '/matrix/rule',
    method: 'put',
    data: data
  })
}

// 删除水质评价依据标准表
export function delRule(evaluaBasis) {
  return request({
    url: '/matrix/rule/' + evaluaBasis,
    method: 'delete'
  })
}
