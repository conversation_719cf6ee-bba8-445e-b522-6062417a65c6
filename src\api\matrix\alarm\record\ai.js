import request from '@/utils/request'

// 查询ai告警列表
export function listAlarm(query) {
  return request({
    url: '/matrix/ai/list',
    method: 'get',
    params: query
  })
}
// 查询用户信息
export function listUserList(query) {
  return request({
    url: '/matrix/ai/userlist',
    method: 'get',
    params: query
  })
}
// 查询ai告警详细
export function getAlarm(sid) {
  return request({
    url: '/matrix/ai/' + sid,
    method: 'get'
  })
}

// 新增ai告警
export function addAlarm(data) {
  return request({
    url: '/matrix/ai',
    method: 'post',
    data: data
  })
}

// 修改ai告警
export function updateAlarm(data) {
  return request({
    url: '/matrix/ai',
    method: 'put',
    data: data
  })
}
// 手动解除告警
export function handleEdit(data) {
  return request({
    url: '/matrix/ai/edit',
    method: 'put',
    data: data
  })
}
// 删除ai告警
export function delAlarm(sid) {
  return request({
    url: '/matrix/ai/' + sid,
    method: 'delete'
  })
}

