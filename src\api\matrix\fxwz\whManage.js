import request from '@/utils/request'

// 查询仓库管理列表
export function listWh(query) {
    return request({
      url: '/matrix/wh/whManage/list',
      method: 'get',
      params: query
    })
  }
  
  // 查询仓库管理详细
  export function getWh(wid) {
    return request({
      url: '/matrix/wh/whManage/' + wid,
      method: 'get'
    })
  }
  
  // 新增仓库管理
  export function addWh(data) {
    return request({
      url: '/matrix/wh/whManage',
      method: 'post',
      data: data
    })
  }
  
  // 修改仓库管理
  export function updateWh(data) {
    return request({
      url: '/matrix/wh/whManage',
      method: 'put',
      data: data
    })
  }
  
  // 删除仓库管理
  export function delWh(wid) {
    return request({
      url: '/matrix/wh/whManage/' + wid,
      method: 'delete'
    })
  }

//物资绑定相关接口
//查询物资绑定列表
export function listByWid(query) {
  return request({
    url: '/matrix/wh/whManage/listByWid',
    method: 'get',
    params: query
  })
}

//物资绑定新增
export function matAdd(data) {
  return request({
    url: '/matrix/wh/whManage/matAdd',
    method: 'post',
    data
  })
}

//查询单个物资绑定
export function getMtByMid(query) {
  return request({
    url: '/matrix/wh/whManage/getMtByMid',
    method: 'get',
    params: query
  })
}

//修改物资绑定
export function matEdit(data) {
  return request({
    url: '/matrix/wh/whManage/matEdit',
    method: 'put',
    data
  })
}

//删除物资绑定
export function matDel(mid) {
  return request({
    url: '/matrix/wh/whManage/matDel/' + mid,
    method: 'delete'
  })
}

//关联工程
//列表
export function getAllEn(query) {
  return request({
    url: '/matrix/wh/whManage/getAllEn',
    method: 'get',
    params:query
  })
}
//点击工程查询仓库列表
export function subList(query) {
  return request({
    url: '/matrix/wh/whManage/subList',
    method: 'get',
    params:query
  })
}

// 工程-仓库
export function relateEn(encd,data) {
  return request({
    url: '/matrix/wh/whManage/relateEn?encd=' + encd,
    method: 'post',
    data
  })
}
//所在河流下拉框
export function basRiverSelect() {
  return request({
    url: '/matrix/section/basRiverSelect',
    method: 'get'
  })
}

//根据工程类型查询工程名称
export function getEnByType(query) {
  return request({
    url: '/matrix/registration/getEnByType',
    method: 'get',
    params:query

  })
}