import request from '@/utils/request'

// 查询大事记列表
export function listEvents(query) {
  return request({
    url: '/matrix/events/list',
    method: 'get',
    params: query
  })
}

// 查询大事记详细
export function getEvents(eid) {
  return request({
    url: '/matrix/events/' + eid,
    method: 'get'
  })
}

// 新增大事记
export function addEvents(data) {
  return request({
    url: '/matrix/events',
    method: 'post',
    data: data
  })
}

// 修改大事记
export function updateEvents(data) {
  return request({
    url: '/matrix/events',
    method: 'put',
    data: data
  })
}

// 删除大事记
export function delEvents(eid) {
  return request({
    url: '/matrix/events/' + eid,
    method: 'delete'
  })
}
