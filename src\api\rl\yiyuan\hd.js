import request from '@/utils/request'

// 查询河段管理列表
export function listHd(query) {
  return request({
    url: '/rl/hd/list',
    method: 'get',
    params: query
  })
}

// 查询所有河段数据
export function listAllHd() {
  return request({
    url: '/rl/hd/listAllHd',
    method: 'get',
  })
}

// 查询河段管理详细
export function getHd(id) {
  return request({
    url: '/rl/hd/' + id,
    method: 'get'
  })
}

// 根据河段编码查询河段管理详细
export function getHdByHdbm(hdbm) {
  return request({
    url: '/rl/hd/getHdInfo/' + hdbm,
    method: 'get'
  })
}

// 新增河段管理
export function addHd(data) {
  return request({
    url: '/rl/hd',
    method: 'post',
    data: data
  })
}

// 修改河段管理
export function updateHd(data) {
  return request({
    url: '/rl/hd',
    method: 'put',
    data: data
  })
}

// 删除河段管理
export function delHd(id) {
  return request({
    url: '/rl/hd/' + id,
    method: 'delete'
  })
}
