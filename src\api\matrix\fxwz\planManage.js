import request from '@/utils/request'

// 查询【预案管理】列表
export function listPreplan(query) {
  return request({
    url: '/zhy-forecast/b/preplan/list',
    method: 'get',
    params: query
  })
}

// 查询【预案管理】详细
export function getPreplan(id) {
  return request({
    url: '/zhy-forecast/b/preplan/' + id,
    method: 'get'
  })
}

// 新增【预案管理】
export function addPreplan(data) {
  return request({
    url: '/zhy-forecast/b/preplan',
    method: 'post',
    data: data
  })
}

// 修改【预案管理】
export function updatePreplan(data) {
  return request({
    url: '/zhy-forecast/b/preplan',
    method: 'put',
    data: data
  })
}

// 删除【预案管理】
export function delPreplan(id) {
  return request({
    url: '/zhy-forecast/b/preplan/' + id,
    method: 'delete'
  })
}
