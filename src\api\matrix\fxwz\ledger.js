import request from '@/utils/request'

// 查询物资出入库台账列表
export function listWzDetailsList(query) {
  return request({
    url: '/matrix/wzDetailsList/list',
    method: 'get',
    params: query
  })
}

// 查询物资出入库台账详细
export function getWzDetailsList(id) {
  return request({
    url: '/matrix/wzDetailsList/' + id,
    method: 'get'
  })
}

// 新增物资出入库台账
export function addWzDetailsList(data) {
  return request({
    url: '/matrix/wzDetailsList',
    method: 'post',
    data: data
  })
}

// 修改物资出入库台账
export function updateWzDetailsList(data) {
  return request({
    url: '/matrix/wzDetailsList',
    method: 'put',
    data: data
  })
}

// 删除物资出入库台账
export function delWzDetailsList(id) {
  return request({
    url: '/matrix/wzDetailsList/' + id,
    method: 'delete'
  })
}
// 仓库列表
export function warehouseList(params) {
  return request({
    url: '/matrix/wzWarehouse/list',
    method: 'get',
    params
  })
}

