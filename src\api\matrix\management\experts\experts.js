import request from '@/utils/request'

// 查询抢险专家列表
export function listExperts(query) {
  return request({
    url: '/matrix/experts/list',
    method: 'get',
    params: query
  })
}

// 查询抢险专家详细
export function getExperts(eid) {
  return request({
    url: '/matrix/experts/' + eid,
    method: 'get'
  })
}

// 新增抢险专家
export function addExperts(data) {
  return request({
    url: '/matrix/experts',
    method: 'post',
    data: data
  })
}

// 修改抢险专家
export function updateExperts(data) {
  return request({
    url: '/matrix/experts',
    method: 'put',
    data: data
  })
}

// 删除抢险专家
export function delExperts(eid) {
  return request({
    url: '/matrix/experts/' + eid,
    method: 'delete'
  })
}
