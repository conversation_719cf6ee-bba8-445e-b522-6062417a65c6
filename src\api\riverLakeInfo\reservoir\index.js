import request from '@/utils/request'

// 查询水库列表
export function listResZibo(query) {
  return request({
    url: '/product/resZibo/list',
    method: 'get',
    params: query
  })
}

// 查询水库详细
export function getResZibo(id) {
  return request({
    url: '/product/resZibo/' + id,
    method: 'get'
  })
}

// 新增水库
export function addResZibo(data) {
  return request({
    url: '/product/resZibo',
    method: 'post',
    data: data
  })
}

// 修改水库
export function updateResZibo(data) {
  return request({
    url: '/product/resZibo',
    method: 'put',
    data: data
  })
}

// 删除水库
export function delResZibo(id) {
  return request({
    url: '/product/resZibo/' + id,
    method: 'delete'
  })
}
