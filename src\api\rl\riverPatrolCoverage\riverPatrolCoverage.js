import request from '@/utils/request'

//按乡镇统计列表
export function listByCountry(query) {
  return request({
    url: '/rl/coverage/getByVill',
    method: 'get',
    params: query
  })
}

//按河段统计列表
export function listByRvier(query) {
  return request({
    url: '/rl/coverage/listByRvier',
    method: 'get',
    params: query
  })
}

//河段统计详细
export function getInfoByRvier(query) {
  return request({
    url: '/rl/record/list',
    method: 'get',
    params: query
  })
}
