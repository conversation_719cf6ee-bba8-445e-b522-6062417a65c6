import request from '@/utils/request'

// 查询调度指令管理列表
export function listDim(query) {
  return request({
    url: '/matrix/dim/list',
    method: 'get',
    params: query
  })
}

// 查询水利工程列表
export function listProj() {
  return request({
    url: '/matrix/dim/list_proj',
    method: 'get'
  })
}

// 查询调度指令管理详细
export function getDim(sid) {
  return request({
    url: '/matrix/dim/' + sid,
    method: 'get'
  })
}

// 新增调度指令管理
export function addDim(data) {
  return request({
    url: '/matrix/dim',
    method: 'post',
    data: data
  })
}

// 修改调度指令管理
export function updateDim(data) {
  return request({
    url: '/matrix/dim',
    method: 'put',
    data: data
  })
}

// 删除调度指令管理
export function delDim(sid) {
  return request({
    url: '/matrix/dim/' + sid,
    method: 'delete'
  })
}
