import request from '@/utils/request'

// 查询安全鉴定列表
export function listAppraisement(query) {
  return request({
    url: '/matrix/appraisement/list',
    method: 'get',
    params: query
  })
}

//根据type查询工程
export function getEnByType(type) {
  return request({
    url: "/matrix/registration/getEnByType",
    method: "get",
    params: type
  })
}

// 查询安全鉴定详细
export function getAppraisement(aid) {
  return request({
    url: '/matrix/appraisement/' + aid,
    method: 'get'
  })
}

// 新增安全鉴定
export function addAppraisement(data) {
  return request({
    url: '/matrix/appraisement',
    method: 'post',
    data: data
  })
}

// 修改安全鉴定
export function updateAppraisement(data) {
  return request({
    url: '/matrix/appraisement',
    method: 'put',
    data: data
  })
}

// 删除安全鉴定
export function delAppraisement(aid) {
  return request({
    url: '/matrix/appraisement/' + aid,
    method: 'delete'
  })
}

//导出安全鉴定
export function appraiseExport(data) {
  return request({
    url: "/matrix/appraisement/export",
    method: "post",
    data: data
  })
}
