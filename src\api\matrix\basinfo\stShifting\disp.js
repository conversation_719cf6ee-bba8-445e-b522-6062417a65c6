import request from '@/utils/request'

// 查询位移测站列表
export function listDisp(query) {
  return request({
    url: '/matrix/disp/list',
    method: 'get',
    params: query
  })
}

// 查询位移测站详细
export function getDisp(rgid) {
  return request({
    url: '/matrix/disp/' + rgid,
    method: 'get'
  })
}

// 新增位移测站
export function addDisp(data) {
  return request({
    url: '/matrix/disp',
    method: 'post',
    data: data
  })
}

// 修改位移测站
export function updateDisp(data) {
  return request({
    url: '/matrix/disp',
    method: 'put',
    data: data
  })
}

// 删除位移测站
export function delDisp(rgid) {
  return request({
    url: '/matrix/disp/' + rgid,
    method: 'delete'
  })
}
export function Projlist() {
  return request({
    url: '/matrix/project/list1',
    method: 'get'
  })
}
export function  sectionlist(data) {
  return request({
    url: '/matrix/project/list2',
    method: 'get',
    params: data
  })
}
export function  allPoints() {
  return request({
    url: '/matrix/project/list3',
    method: 'get'
  })
}
