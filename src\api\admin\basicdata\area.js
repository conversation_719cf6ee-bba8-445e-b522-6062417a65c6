import request from '@/utils/request'

// 查询行政区域列表
export function listArea(query) {
  return request({
    url: '/rl/water/area/list',
    method: 'get',
    params: query
  })
}

// 查询行政区域详细，修改的时候查询
export function getArea(regionCode) {
  return request({
    url: '/rl/water/area/' + regionCode,
    method: 'get'
  })
}

// 查询行政区域列表去除当前选中的节点及上级节点
export function listAreaExcludeChild(regionCode) {
  return request({
    url: '/rl/water/area/list/exclude/' + regionCode,
    method: 'get'
  })
}

// 新增行政区域
export function addArea(data) {
  return request({
    url: '/rl/water/area',
    method: 'post',
    data: data
  })
}

// 修改行政区域
export function updateArea(data) {
  return request({
    url: '/rl/water/area',
    method: 'put',
    data: data
  })
}

// 删除行政区域
export function delArea(regionCode) {
  return request({
    url: '/rl/water/area/' + regionCode,
    method: 'delete'
  })
}

// 获取行政区划下拉树列表
export function areaTreeSelect(query) {
  return request({
    url: '/rl/water/area/areaTreeSelect',
    method: 'get',
    params: query
  })
}

// 查询所有正常使用的行政区域列表
export function listNormalArea() {
    return request({
        url: '/rl/water/area/listArea',
        method: 'get',
    })
}
// 查询行政区域列表(仅查询一级二级节点) 大屏值班信息使用
export function listTown(query) {
  return request({
    url: '/rl/water/area/listTown',
    method: 'get',
    params: query
  })
}
