import request from '@/utils/request'

// 查询人员列表
export function listPerson(encd) {
  return request({
    url: '/matrix/user/list_by_encd?encd=' + encd,
    method: 'get',
  })
}

//根据日期查询值班记录
export function listDutyPersonGroupByDate(query) {
  return request({
    url: '/matrix/duty/list_group_by_date',
    method: 'get',
    params: query
  })
}

//提交-新增
export function addDutyPerson(data) {
  return request({
    url: '/matrix/duty',
    method: 'post',
    data: data
  })
}
