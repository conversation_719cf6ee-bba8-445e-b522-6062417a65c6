import request from '@/utils/request'

// 查询评价标准列表
export function listCriter(query) {
  return request({
    url: '/matrix/criter/list',
    method: 'get',
    params: query
  })
}

// 查询评价标准详细
export function getCriter(cid) {
  return request({
    url: '/matrix/criter/' + cid,
    method: 'get'
  })
}

// 新增评价标准
export function addCriter(data) {
  return request({
    url: '/matrix/criter',
    method: 'post',
    data: data
  })
}

// 修改评价标准
export function updateCriter(data) {
  return request({
    url: '/matrix/criter',
    method: 'put',
    data: data
  })
}

// 删除评价标准
export function delCriter(cid) {
  return request({
    url: '/matrix/criter/' + cid,
    method: 'delete'
  })
}
