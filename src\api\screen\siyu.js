import request from "@/utils/request";

// 预报列表
export function listYubao(params) {
  return request({
    url: '/forecast/screen/yubao/listYubao',
    method: 'get',
    params
  })
}
// 获取断面
//  查询预报水文断面
export function stBprpList(params) {
  return request({
    url: "/zhy-forecast/b/stBprp/getList",
    method: "get",
    params,
  });
}
// 预报水位
export function getStForecastList(params) {
  return request({
    url: "/zhy-forecast/f/stForecast/list",
    method: "get",
    params,
  });
}
//  获取预报结果的【超警戒】和【超保证】和【漫坝】的数量
export function getGrzWrzOutCount(params) {
  return request({
    url: "/zhy-forecast/f/stForecast/getGrzWrzOutCount",
    method: "get",
    params,
  });
}
//  灾情风险 - 响应村庄数量
export function getVillageFloodCount(params) {
  return request({
    url: "/zhy-forecast/f/stForecast/getVillageFloodCount",
    method: "get",
    params,
  });
}
//  大屏预案
export function selectReservePlan(params) {
  return request({
    url: "/zhy-forecast/b/preplan/selectReservePlan",
    method: "get",
    params,
  });
}
//  预案灾情总览
export function getDisasterSituation(params) {
  return request({
    url: "/zhy-forecast/b/preplan/getDisasterSituation",
    method: "get",
    params,
  });
}
//  断面列表排序
export function getSectionList(params) {
  return request({
    url: "/zhy-forecast/b/preplan/getSectionList",
    method: "get",
    params,
  });
}
// 删除预演方案（历史）
export function deleteYyfa(id) {
  return request({
    url: '/forecast/screen/yubao/delete/'+id,
    method: 'delete'
  })
}
// 降雨
export function selectImageWater() {
  return request({
    url: '/forecast/screen/yubao/selectImageWater',
    method: 'get'
  })
}
// 雷达
export function selectRadarImage() {
  return request({
    url: '/forecast/screen/yubao/selectRadarImage',
    method: 'get'
  })
}
// 台风
export function selectTyphoonImage() {
  return request({
    url: '/forecast/screen/yubao/selectTyphoonImage',
    method: 'get'
  })
}
//根据预报方案代码查询预报方案（预报页面上游来水）
export function getForecastBScheme(schemeCode) {
  return request({
    url: "/zhy-forecast/b/scheme/getForecastBScheme/" + schemeCode,
    method: "get",
  });
}
//查询辛集闸 / 孟家庄预报水位流量过程线;
export function getRiverForecast(params) {
  return request({
    url: "/zhy-forecast/b/scheme/getRiverForecast",
    method: "get",
    params,
  });
}
//查询辛集闸 / 孟家庄预报水位流量过程线;
export function getRiverForecast2(params) {
  return request({
    url: "/zhy-forecast/b/scheme/getRiverForecast2",
    method: "get",
    params,
  });
}
//预警 水位弹窗
export function getWaterLevelWarningPop(params) {
  return request({
    url: "/zhy-forecast/f/stForecast/getWaterLevelWarningPop",
    method: "get",
    params,
  });
}
//预报 实测预报弹窗
export function getWaterLevelTrueWarningPop(params) {
  return request({
    url: "/zhy-forecast/f/stForecast/getWaterLevelTrueWarningPop",
    method: "get",
    params,
  });
}
//预演页面的查询进度条
export function getProgressBar(params) {
  return request({
    url: "/zhy-forecast/screen/preview/getProgressBar",
    method: "get",
    params,
  });
}
// 查询断面
export function getHydrologicStation(params) {
  return request({
    url: "/zhy-forecast/b/stBprp/getHydrologicStation",
    method: "get",
    params,
  });
}
//水位预警列表
export function getWaterLevelWarning(params) {
  return request({
    url: "/zhy-forecast/f/stForecast/getWaterLevelWarning",
    method: "get",
    params,
  });
}
// 实测预警
export function getWaterLevelTrueWarning(params) {
  return request({
    url: "/zhy-forecast/f/stForecast/getWaterLevelTrueWarning",
    method: "get",
    params,
  });
}
// 水情信息
export function getAllRiverForecast(stcd) {
  return request({
    url: `/zhy-forecast/b/scheme/getAllRiverForecast2/${stcd}`,
    method: "get",
  });
}
// 流量
export function getAllRiverForecast3(stcd) {
  return request({
    url: `/zhy-forecast/b/scheme/getAllRiverForecast3/${stcd}`,
    method: "get",
  });
}
// 淹没分析结果
export function getAnalysisResult(params) {
  return request({
    url: '/forecast/rgsmstt/list',
    method: 'get',
    params
  })
}
// 雨量站信息
export function getScjyList(params) {
  return request({
    url: '/zhy-forecast/b/stBprp/getScjyList',
    method: 'get',
  })
}
// 雨量站历史数据 raindetection/getYlzxq
export function getYlzxq(params) {
  return request({
    url: '/matrix/raindetection/getYlzxq',
    method: 'get',
    params
  })
}
