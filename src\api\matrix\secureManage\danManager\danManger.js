import request from '@/utils/request'

// 查询险情管理列表
export function listDanManger(query) {
  return request({
    url: '/matrix/danManger/list',
    method: 'get',
    params: query
  })
}

// 查询险情管理详细
export function getDanManger(did) {
  return request({
    url: '/matrix/danManger/' + did,
    method: 'get'
  })
}

// 新增险情管理
export function addDanManger(data) {
  return request({
    url: '/matrix/danManger',
    method: 'post',
    data: data
  })
}

// 修改险情管理
export function updateDanManger(data) {
  return request({
    url: '/matrix/danManger',
    method: 'put',
    data: data
  })
}

// 删除险情管理
export function delDanManger(did) {
  return request({
    url: '/matrix/danManger/' + did,
    method: 'delete'
  })
}
