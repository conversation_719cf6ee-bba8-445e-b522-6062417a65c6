import request from '@/utils/request'

// 查询工情监测列表
export function listMontdetection(query) {
  return request({
    url: '/matrix/montdetection/list',
    method: 'get',
    params: query
  })
}
// 查询工情监测列表
export function listMontdetection2(query) {
  return request({
    url: '/matrix/montdetection/list2',
    method: 'get',
    params: query
  })
}
// 实时查询工情监测列表
export function realTimelistMontdetection(query) {
  return request({
    url: '/matrix/montdetection/gqList',
    method: 'get',
    params: query
  })
}
// 实时查询工情监测列表
export function realTimelistMontdetection2(query) {
  return request({
    url: '/matrix/montdetection/gqList2',
    method: 'get',
    params: query
  })
}

// 查询工情监测详细
export function getMontdetection(did) {
  return request({
    url: '/matrix/montdetection/' + did,
    method: 'get'
  })
}

// 新增工情监测
export function addMontdetection(data) {
  return request({
    url: '/matrix/montdetection',
    method: 'post',
    data: data
  })
}

// 修改工情监测
export function updateMontdetection(data) {
  return request({
    url: '/matrix/montdetection',
    method: 'put',
    data: data
  })
}

// 删除工情监测
export function delMontdetection(did) {
  return request({
    url: '/matrix/montdetection/' + did,
    method: 'delete'
  })
}
