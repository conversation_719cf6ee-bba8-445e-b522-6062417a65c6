import request from '@/utils/request'

// 查询VIEW列表
export function listWrp(query) {
  return request({
    url: '/matrix/section/listAllBasStB',
    method: 'get',
    params: query
  })
}

// 查询VIEW详细
export function getWrp(bid) {
  return request({
    url: '/matrix/section/getSectionRelationBasStB/' + bid,
    method: 'get'
  })
}

// 新增VIEW
export function addWrp(data) {
  return request({
    url: '/matrix/wrp',
    method: 'post',
    data: data
  })
}

// 修改VIEW
export function updateWrp(data) {
  return request({
    url: '/matrix/wrp',
    method: 'put',
    data: data
  })
}

// 删除VIEW
export function delWrp(bid) {
  return request({
    url: '/matrix/wrp/' + bid,
    method: 'delete'
  })
}

//获取所有测站
export function listStation(query) {
  return request({
    url: "/matrix/station/list",
    method: "GET",
    params: query
  })
} 

//保存关联测站编号
export function saveSelectStation(data) {
  return request({
    url: "/matrix/section/saveSectionRelationBasStB",
    method: "post",
    data: data
  })
}

//获取选中的测站
export function selectStation(did) {
  return request({
    url: "/matrix/section/getSectionRelationBasStB/"+ did,
    method: "get",
  })
}