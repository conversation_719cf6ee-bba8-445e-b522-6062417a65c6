import request from '@/utils/request'

// 查询河管员管理列表
export function listRiverManage(query) {
  return request({
    url: '/rl/riverManager/list',
    method: 'get',
    params: query
  })
}

// 查询河管员管理详细
export function getRiverManage(id) {
  return request({
    url: '/rl/riverManager/' + id,
    method: 'get'
  })
}

// 新增河管员管理
export function addRiverManage(data) {
  return request({
    url: '/rl/riverManager',
    method: 'post',
    data: data
  })
}

// 修改河管员管理
export function updateRiverManage(data) {
  return request({
    url: '/rl/riverManager',
    method: 'put',
    data: data
  })
}

// 删除河管员管理
export function delRiverManage(id) {
  return request({
    url: '/rl/riverManager/' + id,
    method: 'delete'
  })
}
