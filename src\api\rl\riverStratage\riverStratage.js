import request from '@/utils/request'

// 查询一河一策列表
export function listNet(query) {
  return request({
    url: '/rl/riverStratage/list',
    method: 'get',
    params: query
  })
}

// 查询一河一策详细
export function getNet(query) {
  return request({
    url: '/rl/riverStratage/info/list',
    method: 'get',
    params: query
  })
}
// 查询文件
export function getFileList(query) {
  return request({
    url: '/rl/riverStratage/file/',
    method: 'get',
    params: query
  })
}
