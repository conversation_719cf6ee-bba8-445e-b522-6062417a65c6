.el-divider--horizontal{
    margin: 6px 0px 12px;
}
.formLabel,.formValue{
    box-sizing: border-box;
    height: 42px;
    line-height: 42px;
    border: 1px solid #cee1f5;
    border-bottom: none;
    text-align: center;

}
.formLabel{
    background-color: #f2f7ff;
    text-align: right;
}



::v-deep .formValue{
    text-align: left;
    padding-left: 5px;
    padding-top: 2px;
    .el-form-item__error{
        width: 100px;
        top: 50%;
        transform: translateY(-50%);
        right: 5px;
        line-height: 1.2;
        left: auto;
    }
    .el-select{
        width: 100%;
        .el-input--medium{
            width: 63%;
        }
    }

    .areaForm{
        width: 160px;
        .el-input--medium{
            width: 100%;
        }
    }
    .el-input{
        width: 63%;
    }
    .el-input-number--medium{
        width: 63%!important;
        .el-input{
            width: 100%;
        }

    }
}
.noLeftBorder{
    border-left: none;
}
.lastRow{
    border-bottom: 1px solid #cee1f5;
}
.reason,.remark, .file{
    height: 130px;
    line-height: 130px;
    ::v-deep .el-textarea__inner{
        margin-top: 2px;
    }
}
.imgFile{
    height: 190px;
    line-height: 190px;
}

.address {
  height: 90px;
  line-height: 90px;

  ::v-deep .el-textarea__inner {
    margin-top: 2px;
  }
}
