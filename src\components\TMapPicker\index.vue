<template>
  <div class="t-map-picker">
    <!-- 地图容器 -->
    <div class="map-container" ref="mapContainer"></div>

    <!-- 地图类型切换按钮 -->
    <div class="map-type-switch">
      <div class="switch-wrapper">
        <div
          class="switch-item"
          :class="{ active: currentMapType === 'normal' }"
          @click="switchMapType('normal')"
        >
          <i class="el-icon-map-location"></i>
          <span>地图</span>
        </div>
        <div
          class="switch-item"
          :class="{ active: currentMapType === 'satellite' }"
          @click="switchMapType('satellite')"
        >
          <i class="el-icon-picture-outline"></i>
          <span>影像</span>
        </div>
      </div>
    </div>

    <!-- 坐标信息 -->
    <div class="coordinate-info" v-if="currentMarker && !readonly">
      <span>经度：{{ coordinates[0] }}</span>
      <span>纬度：{{ coordinates[1] }}</span>
      <el-button type="primary" size="small" @click="confirmLocation"
        >确认选择</el-button
      >
    </div>

    <!-- 操作说明 -->
    <div class="map-tips" v-if="!readonly">
      <i class="el-icon-info"></i>
      点击地图选择位置，点击"确认选择"按钮确定位置
    </div>
  </div>
</template>

<script>
export default {
  name: "TMapPicker",
  props: {
    // 初始中心点
    center: {
      type: Array,
      default: () => [117.97943, 37.39416],
    },
    // 初始缩放级别
    zoom: {
      type: Number,
      default: 12,
    },
    // 初始坐标值（用于回显）
    initialCoordinates: {
      type: Array,
      default: () => [],
    },
    // 只读模式
    readonly: {
      type: Boolean,
      default: false,
    },
    // 标记点名称
    markerName: {
      type: String,
      default: "",
    },
    // 是否显示名称
    showName: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      map: null,
      currentMarker: null,
      coordinates: [],
      isMapReady: false,
      markerLabel: null,
      isNameVisible: true,
      currentMapType: 'satellite', // 新增：当前地图类型
    };
  },
  mounted() {
     this.initMap();
  },
  watch: {
    initialCoordinates: {
      handler(newVal) {
        if (newVal.length) {
          this.coordinates = newVal;
          this.addMarker(newVal);
        }
      },
      deep: true,
      immediate: true,
    },
    showName: {
      handler(newVal) {
        this.isNameVisible = newVal;
        if (this.coordinates.length) {
          this.updateMarkerLabel();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 初始化地图
    initMap() {
      try {
        // 创建地图实例
        this.map = new T.Map(this.$refs.mapContainer, {
          zoom: this.zoom,
        });

        // 设置地图中心点
        this.map.centerAndZoom(new T.LngLat(...this.center));

        // 添加缩放控件
        this.map.addControl(new T.Control.Zoom());

        // 设置为影像图
        this.map.setMapType(window.TMAP_HYBRID_MAP);

        // 允许鼠标滚轮缩放
        this.map.enableScrollWheelZoom();

        // 绑定点击事件
        this.map.addEventListener("click", this.handleMapClick);

        // 标记地图已准备就绪
        this.isMapReady = true;

        // 如果有初始坐标，添加标记
        if (this.initialCoordinates.length === 2) {
          this.addMarker(this.initialCoordinates);
        }

      } catch (error) {
        console.error("地图初始化失败:", error);
        //this.$message.error('地图加载失败，请刷新重试')
      }
    },

    // 处理地图点击
    handleMapClick(e) {
      if (this.readonly) return;
      const lnglat = e.lnglat;
      this.coordinates = [lnglat.lng.toFixed(6), lnglat.lat.toFixed(6)];
      this.addMarker([lnglat.lng, lnglat.lat]);
    },

    // 添加标记
    addMarker(position) {
      if (!this.isMapReady) return;
      // 如果已有标记，先移除
      if (this.currentMarker) {
        this.map.removeOverLay(this.currentMarker);
      }
      if (this.markerLabel) {
        this.map.removeOverLay(this.markerLabel);
      }

      // 创建新标记
      this.currentMarker = new T.Marker(new T.LngLat(...position), {
        icon: new T.Icon({
          iconUrl: require('@/assets/marker/markerA.png'),
          iconSize: new T.Point(19, 25),
          iconAnchor: new T.Point(10, 25),
        }),
      });
      // 添加标记到地图
      this.map.addOverLay(this.currentMarker);

      // 使用InfoWindow
      if (this.markerName && this.isNameVisible) {
        const infoContent = `
          <div class="map-info-window">
            <span class="info-text">${this.markerName}</span>
            ${
              !this.readonly
                ? `
              <span class="info-close" onclick="window.closeMarkerLabel()">
                <svg viewBox="0 0 1024 1024" width="12" height="12">
                  <path d="M512 421.653L871.04 62.592l90.517 90.507-359.04 359.04 358.87 358.901-90.507 90.517L512 602.667 153.173 961.557l-90.506-90.517L421.493 512 62.667 153.099l90.506-90.507L512 421.653z" fill="currentColor"></path>
                </svg>
              </span>
            `
                : ""
            }
          </div>
        `;

        this.markerLabel = new T.InfoWindow(infoContent, {
          offset: new T.Point(-1, 10),
          closeButton: false,
          autoPan: false,
          closeOnClick: false,
          minWidth: 10, // 添加最小宽度
          maxWidth: 1000, // 添加最大宽度
          border: false, // 禁用边框
        });

        // 添加关闭标签的全局方法
        window.closeMarkerLabel = () => {
          this.isNameVisible = false;
          this.$emit("update:showName", false);
          this.map.closeInfoWindow();
        };

        // 打开信息窗口
        this.currentMarker.openInfoWindow(this.markerLabel);
      }

      // 调整视野到标记点
      this.map.panTo(new T.LngLat(...position));
    },

    // 更新标签
    updateMarkerLabel(position) {
      position = position || this.coordinates;

      if (this.markerLabel) {
        this.map.closeInfoWindow();
      }

      if (this.markerName && this.isNameVisible && this.currentMarker) {
        const infoContent = `
          <div class="map-info-window">
            <span class="info-text">${this.markerName}</span>
            ${
              !this.readonly
                ? `
              <span class="info-close" onclick="window.closeMarkerLabel()">
                <svg viewBox="0 0 1024 1024" width="12" height="12">
                  <path d="M512 421.653L871.04 62.592l90.517 90.507-359.04 359.04 358.87 358.901-90.507 90.517L512 602.667 153.173 961.557l-90.506-90.517L421.493 512 62.667 153.099l90.506-90.507L512 421.653z" fill="currentColor"></path>
                </svg>
              </span>
            `
                : ""
            }
          </div>
        `;

        this.markerLabel = new T.InfoWindow(infoContent, {
          offset: new T.Point(-1, -40), // 稍微增加上移距离
          closeButton: false,
          autoPan: false,
          closeOnClick: false,
          minWidth: 10, // 添加最小宽度
          maxWidth: 1000, // 添加最大宽度
          border: false, // 禁用边框
        });

        this.currentMarker.openInfoWindow(this.markerLabel);
      }
    },

    // 确认选择位置
    confirmLocation() {
      if (!this.coordinates.length) {
        this.$message.warning("请先在地图上选择位置");
        return;
      }

      // 触发选择完成事件
      this.$emit("on-pick", {
        lng: this.coordinates[0],
        lat: this.coordinates[1],
      });
    },

    // 清除标记
    clearMarker() {
      if (this.currentMarker) {
        this.map.removeOverLay(this.currentMarker);
        this.currentMarker = null;
      }
      if (this.markerLabel) {
        this.map.closeInfoWindow();
        this.markerLabel = null;
      }
      this.coordinates = [];
      if (window.closeMarkerLabel) {
        delete window.closeMarkerLabel;
      }
    },

    // 设置中心点
    setCenter(position) {
      if (this.isMapReady && position.length === 2) {
        this.map.panTo(new T.LngLat(...position));
      }
    },

    // 禁用地图交互
    disableMapInteractions() {
      // this.map.disableScrollWheelZoom()
      // this.map.disableDragging()
      // this.map.disableDoubleClickZoom()
      // this.map.disableKeyboard()
      // this.map.disableInertia()
    },

    //切换地图类型方法
    switchMapType(type) {
      this.currentMapType = type;
      if (type === 'normal') {
        this.map.setMapType(window.TMAP_NORMAL_MAP);
      } else {
        this.map.setMapType(window.TMAP_HYBRID_MAP);
      }
    },
  },
  beforeDestroy() {
    // 销毁地图实例，清理内存
    if (this.map) {
      this.map.clearOverLays();
      this.map = null;
    }
  },
};
</script>

<style lang="scss" scoped>
.t-map-picker {
  position: relative;
  width: 100%;
  height: 600px;
  border-radius: 4px;

  .map-container {
    width: 100%;
    height: 100%;
  }

  .map-type-switch {
    position: absolute;
    right: 20px;
    bottom: 20px;
    z-index: 1000;

    .switch-wrapper {
      display: flex;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      border: 1px solid #DCDFE6;

      .switch-item {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        cursor: pointer;
        transition: all 0.3s;
        color: #606266;
        font-size: 13px;

        &:first-child {
          border-right: 1px solid #DCDFE6;
        }

        i {
          margin-right: 4px;
          font-size: 14px;
        }

        &:hover {
          background-color: #F5F7FA;
        }

        &.active {
          background-color: #409EFF;
          color: #fff;
        }
      }
    }
  }

  .coordinate-info {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;

    span {
      display: block;
      margin-bottom: 5px;
      font-size: 14px;
      color: #606266;
    }
  }

  .map-tips {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 12px;
    color: #909399;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;

    i {
      margin-right: 5px;
    }
  }
}
// 隐藏天地图logo
  ::v-deep .tdt-control-copyright {
    display: none;
  }

</style>
