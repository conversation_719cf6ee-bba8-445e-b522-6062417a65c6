import request from '@/utils/request'

// 查询测站管理列表
export function listStation(query) {
  return request({
    url: '/matrix/station/list',
    method: 'get',
    params: query
  })
}

// 查询测站管理详细
export function getStation(bid) {
  return request({
    url: '/matrix/station/' + bid,
    method: 'get'
  })
}

// 新增测站管理
export function addStation(data) {
  return request({
    url: '/matrix/station',
    method: 'post',
    data: data
  })
}

// 修改测站管理
export function updateStation(data) {
  return request({
    url: '/matrix/station',
    method: 'put',
    data: data
  })
}

// 删除测站管理
export function delStation(bid) {
  return request({
    url: '/matrix/station/' + bid,
    method: 'delete'
  })
}
//查询工程数组
export function Projlist() {
  return request({
    url: '/matrix/project/list1',
    method: 'get'
  })
}
