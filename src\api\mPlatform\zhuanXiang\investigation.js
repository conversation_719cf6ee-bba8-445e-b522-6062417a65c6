import request from '@/utils/request'

// 查询专项行动-专项排查问题列表
export function listInvestigation(query) {
  return request({
    url: '/rl/investigation/list',
    method: 'get',
    params: query
  })
}

// 查询专项行动-专项排查问题详细
export function getInvestigation(id) {
  return request({
    url: '/rl/investigation/' + id,
    method: 'get'
  })
}

// 新增专项行动-专项排查问题
export function addInvestigation(data) {
  return request({
    url: '/rl/investigation',
    method: 'post',
    data: data
  })
}

// 修改专项行动-专项排查问题
export function updateInvestigation(data) {
  return request({
    url: '/rl/investigation',
    method: 'put',
    data: data
  })
}

// 删除专项行动-专项排查问题
export function delInvestigation(id) {
  return request({
    url: '/rl/investigation/' + id,
    method: 'delete'
  })
}
