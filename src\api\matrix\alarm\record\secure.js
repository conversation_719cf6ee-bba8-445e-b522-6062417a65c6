import request from '@/utils/request'

// 查询安全告警列表
export function listAlarm(query) {
  return request({
    url: '/matrix/osm_alarm/list',
    method: 'get',
    params: query
  })
}

// 查询安全告警详细
export function getAlarm(sid) {
  return request({
    url: '/matrix/osm_alarm/' + sid,
    method: 'get'
  })
}

// 新增安全告警
export function addAlarm(data) {
  return request({
    url: '/matrix/osm_alarm',
    method: 'post',
    data: data
  })
}

// 修改安全告警
export function updateAlarm(data) {
  return request({
    url: '/matrix/osm_alarm',
    method: 'put',
    data: data
  })
}
// 手动解除告警
export function handleEdit(data) {
  return request({
    url: '/matrix/osm_alarm/edit',
    method: 'put',
    data: data
  })
}
// 删除安全告警
export function delAlarm(sid) {
  return request({
    url: '/matrix/osm_alarm/' + sid,
    method: 'delete'
  })
}
