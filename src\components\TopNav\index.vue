<template>
  <el-menu
    :default-active="activeMenu"
    mode="horizontal"
    @select="handleSelect"
  >
    <template v-for="(item, index) in topMenus">
      <el-menu-item :style="{'--theme': theme}" :index="item.path" :key="index"
                    v-if="index < visibleNumber"
      >
        <svg-icon :icon-class="item.meta.icon" v-show="item.meta.icon!=='#'"/>
        <div class="menu-text-box" :style="{width: item.meta.icon === '#'? '100px': '70px'}">{{ item.meta.title }}</div>
      </el-menu-item
      >
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <el-submenu :style="{'--theme': theme}" index="more" v-if="topMenus.length > visibleNumber">
      <template slot="title">更多菜单</template>
      <template v-for="(item, index) in topMenus">
        <el-menu-item
          :index="item.path"
          :key="index"
          v-if="index >= visibleNumber"
        >
          <svg-icon :icon-class="item.meta.icon"/>
          {{ item.meta.title }}
        </el-menu-item
        >
      </template>
    </el-submenu>
    <span class="active-back"></span>
  </el-menu>
</template>

<script>
import {constantRoutes} from "@/router";
import { listMenu } from "@/api/system/moduleMenu";
// 隐藏侧边栏路由
const hideList = ['/index', '/user/profile'];

export default {
  data() {
    return {
      // 顶部栏初始数
      visibleNumber: 8,
      // 当前激活菜单的 index
      currentIndex: undefined,
      // 后台模块管理：存放模块关联关系
      moduleOptions: []
    };
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme;
    },
    // 顶部显示菜单
    topMenus() {
      let topMenus = [];
      if(localStorage.getItem('sysModule')) {
        this.$store.commit('app/SET_MODULE_NAME', localStorage.getItem('sysModule'))
      }
      let module = this.$store.getters.currentModule
      if(this.moduleOptions.length) {
        let moduleSystem =  this.moduleOptions.find((item) => item.menuName === module)
        let moduleList = moduleSystem.children.map((item) => item.menuName)
        this.routers.map((menu) => {
          if (menu.hidden !== true) {
            // 兼容顶部栏一级菜单内部跳转
            if (menu.path === "/") {
              topMenus.push(menu.children[0]);
            } else {
              if(moduleList.includes(menu.meta.title)) {
                topMenus.push(menu);
              }
            }
          }
        });
      }
      return topMenus;
    },
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            if (router.path === "/") {
              router.children[item].path = "/" + router.children[item].path;
            } else {
              if (!this.ishttp(router.children[item].path)) {
                router.children[item].path = router.path + "/" + router.children[item].path;
              }
            }
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
    // 默认激活的菜单
    activeMenu() {
      const path = this.$route.path;
      let activePath = path;
      if (path !== undefined && path.lastIndexOf("/") > 0 && hideList.indexOf(path) === -1) {
        const tmpPath = path.substring(1, path.length);
        activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
        if (!this.$route.meta.link) {
          this.$store.dispatch('app/toggleSideBarHide', false);
        }
      } else if (!this.$route.children) {
        activePath = path;
        this.$store.dispatch('app/toggleSideBarHide', true);
      }
      this.activeRoutes(activePath);
      return activePath;
    },
  },
  beforeMount() {
    window.addEventListener('resize', () => {
      this.setVisibleNumber()
      // this.initActiveMenu()
    })
  },
  created() {
    this.getListModule()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setVisibleNumber)
  },
  mounted() {
    this.setVisibleNumber();
    // this.$nextTick(() => {
    //   this.initActiveMenu()
    // })
  },
  methods: {
    // 根据宽度计算设置显示栏数
    setVisibleNumber() {
      const width = document.body.getBoundingClientRect().width / 2;
      this.visibleNumber = parseInt(width / 110);
    },
    // 刷新时根据菜单显示选中
    initActiveMenu() {
      let menu = this.$refs.menuItem0[0]
      let width = menu.$el.clientWidth
      let activeBack = document.getElementsByClassName('active-back')[0]
      activeBack.style.width = width + 'px'
      const path = this.$route.path
      const tmpPath = path.substring(1, path.length);
      const activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
      const roureIndex = this.routers.findIndex(item => item.path === activePath);
      this.setActiveBox(roureIndex)
    },
    // 菜单选择事件
    handleSelect(key, keyPath) {
      this.currentIndex = key;
      const route = this.routers.find(item => item.path === key);
      const roureIndex = this.routers.findIndex(item => item.path === key);
      // this.setActiveBox(roureIndex)
      if (this.ishttp(key)) {
        // http(s):// 路径新窗口打开
        window.open(key, "_blank");
      } else if (!route || !route.children) {
        // 没有子路由路径内部打开
        const routeMenu = this.childrenMenus.find(item => item.path === key);
        if (routeMenu && routeMenu.query) {
          let query = JSON.parse(routeMenu.query);
          this.$router.push({path: key, query: query});
        } else {
          this.$router.push({path: key});
        }
        this.$store.dispatch('app/toggleSideBarHide', true);
      } else {
        // 显示左侧联动菜单
        this.activeRoutes(key);
        this.$store.dispatch('app/toggleSideBarHide', false);
      }
    },
    setActiveBox(index) {
      let activeBack = document.getElementsByClassName('active-back')[0]
      if (index === -1) {
        activeBack.style.display = 'none'
      } else {
        let menu = this.$refs['menuItem' + index][0].$el
        activeBack.style.display = 'block'
        activeBack.style.left = (menu.offsetLeft - 2) + 'px'
      }
    },
    // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      if (routes.length > 0) {
        this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
      } else {
        this.$store.dispatch('app/toggleSideBarHide', true);
      }
    },
    ishttp(url) {
      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
    },
    getListModule() {
      listMenu().then((response) =>{
        this.moduleOptions = this.handleTree(response.data, "menuId");
      })
    }
  },
};
</script>

<style lang="scss">
.topmenu-container.el-menu--horizontal > .el-menu-item {
  float: left;
  height: 64px !important;
  line-height: 64px !important;
  color: #ffffff !important;
  padding: 0 !important;
  margin: 2px 2px 0 0 !important;
  font-size: 14px;
  font-weight: 600;
  width: 130px;
  text-align: center;
  background: url("~@/assets/images/matrix/menu_bg.png") no-repeat !important;
  background-size: 100% 55% !important;
  background-position-y: 15px !important;
  .menu-text-box{
    display: inline-block;
    width: 70px;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
  width: 130px;
  height: 100%;
  border-bottom-style: none !important;
  color: #303133;
  position: relative;
  overflow: hidden;
  background: url("~@/assets/images/matrix/menu_active.png") no-repeat !important;
  background-size: 100% 55% !important;
  background-position-y: 15px !important;
  text-align: center;
  transition: left 0.5s;
}

@keyframes menuActive {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0.25;
  }
  50% {
    opacity: 0.5;
  }
  75% {
    opacity: 0.75;
  }
  100% {
    opacity: 1;
  }
}

/* submenu item */
.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {
  float: left;
  height: 64px !important;
  line-height: 64px !important;
  color: #ffffff !important;
  padding: 0 5px !important;
  margin: 2px 5px 0px 5px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  width: 110px;
  text-align: center;

  i {
    color: #FFFFFF !important;
  }
}

.el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background: transparent;
}

.topmenu-container.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus {
  width: 130px;
  background: url("~@/assets/images/matrix/menu_active.png") no-repeat !important;
  background-size: 100% 55% !important;
  background-position-y: 15px !important;
}

.topmenu-container.el-menu.el-menu--horizontal {
  border-style: none;
  position: relative;

  .active-back {
    position: absolute;
    display: block;
    height: 30px;
    width: 0;
    top: 20%;
    left: 0;
    border-radius: 6px;
    transition: left 0.5s;
  }
}
</style>
