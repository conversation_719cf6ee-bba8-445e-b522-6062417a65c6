import request from '@/utils/request'

// 查询界桩列列表
export function listBoundaryStone(query) {
  return request({
    url: '/rl/boundaryStone/list',
    method: 'get',
    params: query
  })
}

// 查询界桩详细列表
export function boundaryInfo(query) {
  return request({
    url: '/rl/boundaryStone/boundaryInfo',
    method: 'get',
    params: query
  })
}

//获取河流界桩
export function boundaryStone(rvcd) {
  return request({
    url: '/rl/boundaryStone/map?rvcd=' + rvcd,
    method: 'get'
  })
}

