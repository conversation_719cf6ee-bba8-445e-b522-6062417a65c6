import request from '@/utils/request'

// 查询水质告警列表
export function listAlarm(query) {
  return request({
    url: '/matrix/wqn_alarm/list',
    method: 'get',
    params: query
  })
}

// 查询水质告警详细
export function getAlarm(sid) {
  return request({
    url: '/matrix/wqn_alarm/' + sid,
    method: 'get'
  })
}

// 新增水质告警
export function addAlarm(data) {
  return request({
    url: '/matrix/wqn_alarm',
    method: 'post',
    data: data
  })
}

// 修改水质告警
export function updateAlarm(data) {
  return request({
    url: '/matrix/wqn_alarm',
    method: 'put',
    data: data
  })
}

// 删除水质告警
export function delAlarm(sid) {
  return request({
    url: '/matrix/wqn_alarm/' + sid,
    method: 'delete'
  })
}

// 手动解除告警
export function handleEdit(data) {
  return request({
    url: '/matrix/wqn_alarm/edit',
    method: 'put',
    data: data
  })
}

