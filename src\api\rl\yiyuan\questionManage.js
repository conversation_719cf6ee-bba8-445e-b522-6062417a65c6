import request from '@/utils/request'

// 查询列表
export function listNet(query) {
  return request({
    url: '/rl/questionManage/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getNet(id) {
  return request({
    url: '/rl/questionManage/' + id,
    method: 'get'
  })
}

// 新增
export function addNet(data) {
  return request({
    url: '/rl/questionManage',
    method: 'post',
    data: data
  })
}

// 修改
export function updateNet(data) {
  return request({
    url: '/rl/questionManage',
    method: 'put',
    data: data
  })
}

// 删除
export function delNet(id) {
  return request({
    url: '/rl/questionManage/' + id,
    method: 'delete'
  })
}

//二级弹窗详细
export function getQuestionDetail(id) {
  return request({
    url: '/rl/questionManage/getQuestionLog?id=' + id,
    method: 'get'
  })
}
