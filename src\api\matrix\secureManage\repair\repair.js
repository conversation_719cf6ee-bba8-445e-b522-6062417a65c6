import request from '@/utils/request'

// 查询除险加固列表
export function listRepair(query) {
  return request({
    url: '/matrix/repair/list',
    method: 'get',
    params: query
  })
}

// 查询除险加固详细
export function getRepair(rid) {
  return request({
    url: '/matrix/repair/' + rid,
    method: 'get'
  })
}

// 新增除险加固
export function addRepair(data) {
  return request({
    url: '/matrix/repair',
    method: 'post',
    data: data
  })
}

// 修改除险加固
export function updateRepair(data) {
  return request({
    url: '/matrix/repair',
    method: 'put',
    data: data
  })
}

// 删除除险加固
export function delRepair(rid) {
  return request({
    url: '/matrix/repair/' + rid,
    method: 'delete'
  })
}
export function listPro(query) {
  return request({
    url: '/matrix/project/list1',
    method: 'get',
    params: query
  })
}
