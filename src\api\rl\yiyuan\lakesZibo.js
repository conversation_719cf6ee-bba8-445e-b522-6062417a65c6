import request from '@/utils/request'

// 查询湖泊管理列表
export function listNet(query) {
  return request({
    url: '/rl/baslakesInfo/list',
    method: 'get',
    params: query
  })
}

// 查询湖泊管理详细
export function getNet(id) {
  return request({
    url: '/rl/baslakesInfo/' + id,
    method: 'get'
  })
}

// 新增湖泊管理
export function addNet(data) {
  return request({
    url: '/rl/baslakesInfo',
    method: 'post',
    data: data
  })
}

// 修改湖泊管理
export function updateNet(data) {
  return request({
    url: '/rl/baslakesInfo',
    method: 'put',
    data: data
  })
}

// 删除湖泊管理
export function delNet(id) {
  return request({
    url: '/rl/baslakesInfo/' + id,
    method: 'delete'
  })
}
