import request from '@/utils/request'

// 查询水质管理列表
export function listWaterquality(query) {
  return request({
    url: '/rl/waterquality/list',
    method: 'get',
    params: query
  })
}

// 查询水质管理详细
export function getWaterquality(stcd) {
  return request({
    url: '/rl/waterquality/' + stcd,
    method: 'get'
  })
}

// 新增水质管理
export function addWaterquality(data) {
  return request({
    url: '/rl/waterquality',
    method: 'post',
    data: data
  })
}

// 修改水质管理
export function updateWaterquality(data) {
  return request({
    url: '/rl/waterquality',
    method: 'put',
    data: data
  })
}

// 删除水质管理
export function delWaterquality(stcd) {
  return request({
    url: '/rl/waterquality/' + stcd,
    method: 'delete'
  })
}
