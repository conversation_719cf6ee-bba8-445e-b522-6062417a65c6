import request from '@/utils/request'

// 查询水质告警阈值列表
export function listRule(query) {
  return request({
    url: '/matrix/wqnmispd/rule/list',
    method: 'get',
    params: query
  })
}

// 查询水质告警阈值详细
export function getRule(sid) {
  return request({
    url: '/matrix/wqnmispd/rule/' + sid,
    method: 'get'
  })
}

// 新增水质告警阈值
export function addRule(data) {
  return request({
    url: '/matrix/wqnmispd/rule',
    method: 'post',
    data: data
  })
}

// 修改水质告警阈值
export function updateRule(data) {
  return request({
    url: '/matrix/wqnmispd/rule',
    method: 'put',
    data: data
  })
}

// 删除水质告警阈值
export function delRule(sid) {
  return request({
    url: '/matrix/wqnmispd/rule/' + sid,
    method: 'delete'
  })
}

// 查询评价依据下拉框
export function listBasis(query) {
  return request({
    url: '/matrix/basis/listNoPage',
    method: 'get',
    params: query
  })
}

// 查询评价依据标准列表
export function listWqNmispDStandard(query) {
  return request({
    url: '/matrix/wqnmispd/standard/list',
    method: 'get',
    params: query
  })
}

// 查询水质告警指标记录列表(告警区间列表)
export function listAlarmIndicators(query) {
  return request({
    url: '/matrix/wqnmispd/recode/list',
    method: 'get',
    params: query
  })
}

//删除水质告警指标记录
export function delAlarmIndicators(aid) {
  return request({
    url: '/matrix/wqnmispd/recode/' + aid,
    method: 'delete'
  })
}
// 修改水质告警指标记录
export function updateAlarmIndicators(data) {
  return request({
    url: '/matrix/wqnmispd/recode',
    method: 'put',
    data: data
  })
}
