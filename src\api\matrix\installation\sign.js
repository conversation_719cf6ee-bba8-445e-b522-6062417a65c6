import request from '@/utils/request'

// 查询标识标牌(水库)列表
export function listSign(query) {
  return request({
    url: '/matrix/sign/list',
    method: 'get',
    params: query
  })
}

// 查询标识标牌(水库)详细
export function getSign(sid) {
  return request({
    url: '/matrix/sign/' + sid,
    method: 'get'
  })
}

// 新增标识标牌(水库)
export function addSign(data) {
  return request({
    url: '/matrix/sign',
    method: 'post',
    data: data
  })
}

// 修改标识标牌(水库)
export function updateSign(data) {
  return request({
    url: '/matrix/sign',
    method: 'put',
    data: data
  })
}

// 删除标识标牌(水库)
export function delSign(sid) {
  return request({
    url: '/matrix/sign/' + sid,
    method: 'delete'
  })
}
