import request from '@/utils/request'

// 查询规章制度列表
export function listRegulation(query) {
  return request({
    url: '/product/management/regulation/list',
    method: 'get',
    params: query
  })
}

// 查询规章制度详细
export function getRegulation(id) {
  return request({
    url: '/product/management/regulation/' + id,
    method: 'get'
  })
}

// 新增规章制度
export function addRegulation(data) {
  return request({
    url: '/product/management/regulation',
    method: 'post',
    data: data
  })
}

// 修改规章制度
export function updateRegulation(data) {
  return request({
    url: '/product/management/regulation',
    method: 'put',
    data: data
  })
}

// 删除规章制度
export function delRegulation(id) {
  return request({
    url: '/product/management/regulation/' + id,
    method: 'delete'
  })
}
