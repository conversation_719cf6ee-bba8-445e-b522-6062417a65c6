import request from '@/utils/request'

// 查询供水管理列表
export function listWaterSupply(query) {
  return request({
    url: '/matrix/waterSupply/list',
    method: 'get',
    params: query
  })
}

// 查询供水管理详细
export function getWaterSupply(id) {
  return request({
    url: '/matrix/waterSupply/' + id,
    method: 'get'
  })
}

// 新增供水管理
export function addWaterSupply(data) {
  return request({
    url: '/matrix/waterSupply',
    method: 'post',
    data: data
  })
}

// 修改供水管理
export function updateWaterSupply(data) {
  return request({
    url: '/matrix/waterSupply',
    method: 'put',
    data: data
  })
}

// 删除供水管理
export function delWaterSupply(id) {
  return request({
    url: '/matrix/waterSupply/' + id,
    method: 'delete'
  })
}
