﻿<krpano>

	<!-- Ultra Light Design -->

	<!-- modify the <skin_settings> values -->
	<skin_settings title="false"
	               layout_width="100%"
	               layout_maxwidth="814"
	               controlbar_width="-24"
	               controlbar_height="40"
	               controlbar_offset.normal="30"
	               controlbar_offset.mobile="20"
	               controlbar_offset_closed="-40"
	               controlbar_overlap="0"
	               design_skin_images="vtourskin_light.png"
	               design_bgcolor="0x2D3E50"
	               design_bgalpha="0.0"
	               design_bgborder="0,0,1,0 0xFFFFFF 1"
	               design_bgroundedge="0"
	               design_bgshadow="0"
	               design_thumbborder_bgborder="1 0xFFFFFF 0.8"
	               design_thumbborder_padding="0"
	               design_thumbborder_bgroundedge="0"
	               design_text_css="color:#FFFFFF; font-family:Arial; font-weight:lighter;"
	               design_text_shadow="0"
	               />

	<!-- webvr button style (adjust to match skin style) -->
	<style name="webvr_button_style"
	       border="false"
	       roundedge="calc:1.0"
	       backgroundcolor="get:skin_settings.design_bgcolor" backgroundalpha="get:skin_settings.design_bgalpha"
	       shadow="0.01" shadowrange="10.0" shadowangle="90.0" shadowcolor="0x30261B" shadowalpha="0.50"
	       css="calc:skin_settings.design_text_css + ' font-size:' + 20*webvr_setup_scale*webvr_button_scale + 'px;'"
	       />

	<!-- adjust video controls to match skin style -->
	<layer name="skin_layer">
		<layer name="skin_scroll_window">
			<layer name="skin_scroll_layer">
				<layer name="skin_video_controls">
					<layer name="skin_video_seekbar_container">
						<layer name="skin_video_seekbar" height="1" y="4">
							<layer name="skin_video_loadbar" height="1" />
							<layer name="skin_video_seekpos" bgroundedge="2" width="6" height="6" />
						</layer>
					</layer>
					<layer name="skin_video_time" y="-4" />
				</layer>
			</layer>
		</layer>
	</layer>
					

	<!-- contextmenu style (adjust to match skin style) -->
	<contextmenu customstyle="default|default|default|0x7F000000|0xFFFFFF|0xBBBBBB|0|0x20FFFFFF|1|0|0|0|0xFFFFFF|0|0xFFFFFF|4|6|7|0xAAFFFFFF|none|3|0|0|0|3|0xFFFFFF|0xFFFFFF|0x000000|12|8" />

</krpano>
