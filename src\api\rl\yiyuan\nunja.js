import request from '@/utils/request'

// 查询湿地列表
export function listNunja(query) {
  return request({
    url: '/rl/nunja/list',
    method: 'get',
    params: query
  })
}

// 查询湿地详细
export function getNunja(id) {
  return request({
    url: '/rl/nunja/' + id,
    method: 'get'
  })
}

// 新增湿地
export function addNunja(data) {
  return request({
    url: '/rl/nunja',
    method: 'post',
    data: data
  })
}

// 修改湿地
export function updateNunja(data) {
  return request({
    url: '/rl/nunja',
    method: 'put',
    data: data
  })
}

// 删除湿地
export function delNunja(id) {
  return request({
    url: '/rl/nunja/' + id,
    method: 'delete'
  })
}
