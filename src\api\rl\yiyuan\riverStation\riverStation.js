import request from '@/utils/request'

// 查询河道测站信息列表
export function listStation(query) {
  return request({
    url: '/rl/riverStation/list',
    method: 'get',
    params: query
  })
}

// 查询河道测站信息详细
export function getStation(id) {
  return request({
    url: '/rl/riverStation/' + id,
    method: 'get'
  })
}

// 新增河道测站信息
export function addStation(data) {
  return request({
    url: '/rl/riverStation',
    method: 'post',
    data: data
  })
}

// 修改河道测站信息
export function updateStation(data) {
  return request({
    url: '/rl/riverStation',
    method: 'put',
    data: data
  })
}

// 删除河道测站信息
export function delStation(id) {
  return request({
    url: '/rl/riverStation/' + id,
    method: 'delete'
  })
}
