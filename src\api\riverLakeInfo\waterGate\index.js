import request from '@/utils/request'

// 查询水闸工程管理列表
export function listSluice(query) {
  return request({
    url: '/product/water/sluice/list',
    method: 'get',
    params: query
  })
}

//管理平台专用
export function listSluiceManage(query) {
    return request({
        url: '/product/water/sluice/listManage',
        method: 'get',
        params: query
    })
}

// 查询水闸工程管理详细
export function getSluice(id) {
  return request({
    url: '/product/water/sluice/' + id,
    method: 'get'
  })
}

// 查询水闸名称
export function getSluiceName() {
  return request({
    url: '/product/water/sluice/nm' ,
    method: 'get'
  })
}

// 新增水闸工程管理
export function addSluice(data) {
  return request({
    url: '/product/water/sluice',
    method: 'post',
    data: data
  })
}

// 修改水闸工程管理
export function updateSluice(data) {
  return request({
    url: '/product/water/sluice',
    method: 'put',
    data: data
  })
}

// 删除水闸工程管理
export function delSluice(id) {
  return request({
    url: '/product/water/sluice/' + id,
    method: 'delete'
  })
}

// 查询河流名称
export function getRVName() {
    return request({
        url: '/product/water/sluice/rv' ,
        method: 'get'
    })
}

// 查询河流名称
export function getRvcd(rvnm) {
    return request({
        url: '/product/water/sluice/cd?name='+ rvnm,
        method: 'get'
    })
}


//根据水闸编码查询水闸-水闸数字孪生页面
export function getSluiceInfo(gatecd) {
    return request({
        url: '/product/water/sluice/gateInfo?gatecd=' + gatecd,
        method: 'get'
    })
}

