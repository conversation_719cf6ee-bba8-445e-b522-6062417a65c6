import request from '@/utils/request'

// 查询公示牌列表
export function listBillboard(query) {
  return request({
    url: '/rl/billboard/list',
    method: 'get',
    params: query
  })
}

// 查询公示牌详细
export function getBillboard(id) {
  return request({
    url: '/rl/billboard/' + id,
    method: 'get'
  })
}

// 新增公示牌
export function addBillboard(data) {
  return request({
    url: '/rl/billboard',
    method: 'post',
    data: data
  })
}

// 修改公示牌
export function updateBillboard(data) {
  return request({
    url: '/rl/billboard',
    method: 'put',
    data: data
  })
}

// 删除公示牌
export function delBillboard(id) {
  return request({
    url: '/rl/billboard/' + id,
    method: 'delete'
  })
}
