import request from '@/utils/request'

// 查询水库自然属性列表
export function listProperty(query) {
    return request({
        url: '/product/water/basResProperty/list',
        method: 'get',
        params: query
    })
}

// 查询主要入库/出库河流列表
export function listOut(query) {
    return request({
        url: '/product/water/reservoirInOut/list',
        method: 'get',
        params: query
    })
}

// 查询河湖长信息管理列表
export function listChief(query) {
    return request({
        url: '/product/water/ReservoirChief/list',
        method: 'get',
        params: query
    })
}

//查询监控设备信息
export function listMonitor(query) {
    return request({
        url: '/product/water/deviceMonitor/list',
        method: 'get',
        params: query
    })
}

// 分类动态信息-取水口信息列表
export function listIntake(query) {
    return request({
        url: '/product/waterIntake/intake/list',
        method: 'get',
        params: query
    })
}

// 分类信息-排污口信息列表
export function listOutlet(query) {
    return request({
        url: '/product/water/drainOutlet/list',
        method: 'get',
        params: query
    })
}

// 查询水生态保护区列表
export function listProtect(query) {
    return request({
        url: '/product/water/ecologyProtect/list',
        method: 'get',
        params: query
    })
}

// 查询分类动态信息-堤防信息列表
export function dikeListInfo(query) {
    return request({
        url: '/product/water/dikeListInfo/list',
        method: 'get',
        params: query
    })
}

// 查询一库一档-水闸信息列表
export function listSluice(query) {
    return request({
        url: '/product/water/reservoirSluice/list',
        method: 'get',
        params: query
    })
}

// 查询泵站信息
export function listPumpst(query) {
    return request({
        url: '/product/water/pumpstInfo/list',
        method: 'get',
        params: query
    })
}

