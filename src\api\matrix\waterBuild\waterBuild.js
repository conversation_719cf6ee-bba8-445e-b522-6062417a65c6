import request from '@/utils/request'

// 查询水文化列表
export function listwaterBuild(query) {
  return request({
    url: '/matrix/waterBuild/list',
    method: 'get',
    params: query
  })
}

// 查询水文化详细
export function getwaterBuild(id) {
  return request({
    url: '/matrix/waterBuild/detail/' + id,
    method: 'get'
  })
}

// 新增水文化
export function addwaterBuild(data) {
  return request({
    url: '/matrix/waterBuild/add',
    method: 'post',
    data: data
  })
}

// 修改水文化
export function updatewaterBuild(data) {
  return request({
    url: '/matrix/waterBuild/update',
    method: 'put',
    data: data
  })
}

// 删除水文化
  export function delwaterBuild(id) {
    return request({
      url: '/matrix/waterBuild/delete/' + id,
      method: 'delete'
    })
}
