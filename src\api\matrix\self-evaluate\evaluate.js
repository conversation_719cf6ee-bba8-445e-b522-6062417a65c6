import request from '@/utils/request'
export function listEvalManagement(query) {
  return request({
    url: '/matrix/evalManagement/list',
    method: 'get',
    params: query
  })
}
export function detailManage(query) {
  return request({
    url: `/matrix/evalManagement/${query}`,
    method: 'get'
  })
}
export function listProblem(query) {
  return request({
    url: `/matrix/problem/listByEid/${query}`,
    method: 'get'
  })
}
export function saveManage(data) {
  return request({
    url: `/matrix/evalManagement`,
    method: 'put',
    data
  })
}
export function problemList(query) {
  return request({
    url: '/matrix/problem/list',
    method: 'get',
    params: query
  })
}

export function problemById(query) {
  return request({
    url: `/matrix/problem/${query}`,
    method: 'get',
  })
}

export function problem(data) {
  return request({
    url: `/matrix/problem`,
    method: 'put',
    data
  })
}