import request from '@/utils/request'

// 查询河湖健康评价列表
export function listAssessment(query) {
  return request({
    url: '/rl/water/assessment/list',
    method: 'get',
    params: query
  })
}

// 查询河湖健康评价详细
export function getAssessment(id) {
  return request({
    url: '/rl/water/assessment/' + id,
    method: 'get'
  })
}

// 新增河湖健康评价
export function addAssessment(data) {
  return request({
    url: '/rl/water/assessment',
    method: 'post',
    data: data
  })
}

// 修改河湖健康评价
export function updateAssessment(data) {
  return request({
    url: '/rl/water/assessment',
    method: 'put',
    data: data
  })
}

// 删除河湖健康评价
export function delAssessment(id) {
  return request({
    url: '/rl/water/assessment/' + id,
    method: 'delete'
  })
}
