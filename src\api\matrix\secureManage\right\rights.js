import request from '@/utils/request'

// 查询注册登记列表
export function listRegistration(query) {
  return request({
    url: '/matrix/right/list',
    method: 'get',
    params: query
  })
}

//根据工程类型查询名称
export function getEnByType(type) {
  return request({
    url: "/matrix/registration/getEnByType",
    method: "get",
    params: type
  })
}

//查询等级列表所有列表
export function listProjectionType() {
  return request({
    url: "/matrix/wh/whManage/getAllEn",
    method: "get"
  })
}

// 查询注册登记详细
export function getRegistration(rgid) {
  return request({
    url: '/matrix/right/' + rgid,
    method: 'get'
  })
}

// 新增注册登记
export function addRegistration(data) {
  return request({
    url: '/matrix/right',
    method: 'post',
    data: data
  })
}

// 修改注册登记
export function updateRegistration(data) {
  return request({
    url: '/matrix/right',
    method: 'put',
    data: data
  })
}

// 删除注册登记
export function delRegistration(rgid) {
  return request({
    url: '/matrix/right/' + rgid,
    method: 'delete'
  })
}

//导出注册等级
export function registerExport(data) {
  return request({
    url: "/matrix/right/export",
    method: "post",
    data: data
  })
}
