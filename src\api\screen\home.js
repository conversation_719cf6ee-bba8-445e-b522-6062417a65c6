import request from '@/utils/request'

// 运管-水利工程列表
export function getGcgk() {
  return request({
    url: '/matrix/waterquality/getetype',
    method: 'get',
  })
}
// 运管-水利工程列表--新
export function projectList(){
  return request({
    url: '/matrix/project/list',
    method: 'get'
  })
}
// 运管-工程概况
export function getGcgkById(params) {
  return request({
    url: '/matrix/screen/getGcgkById',
    method: 'get',
    params
  })
}
// 运管-设备设施
export function getSbss(params) {
  return request({
    url: '/matrix/screen/getSbss',
    method: 'get',
    params
  })
}
// 运管-设备设施管理数量
export function getSbssNew(params) {
  return request({
    url: '/matrix/equip/list',
    method: 'get',
    params
  })
}
// 运管-预案管理
export function getYagl(params) {
  return request({
    url: '/matrix/screen/getYagl',
    method: 'get',
    params
  })
}
// 运管-预案管理  新
export function getYaglNew(params) {
  return request({
    url: '/zhy-forecast/b/preplan/list',
    method: 'get',
    params
  })
}

// 运管-标识标牌
export function getBsbp(params) {
  return request({
    url: '/matrix/screen/getBsbp',
    method: 'get',
    params
  })
}
// 运管-管理制度
export function getGlzd(params) {
  return request({
    url: '/matrix/screen/getGlzd',
    method: 'get',
    params
  })
}
// 运管-管理制度 新
export function listDoc(query) {
  return request({
    url: '/matrix/doc/list',
    method: 'get',
    params: query
  })
}
// 河湖-河湖长概况
export function queryHhzzgkTotal(data) {
  return request({
    url: '/rl/szhhdp/queryHhzzgkTotal',
    method: 'post',
    data
  })
}
// 河湖-巡河统计
export function queryXhtj() {
  return request({
    url: '/rl/szhhdp/queryXhtj',
    method: 'post',
  })
}
// 河湖-河湖评价
export function queryHhpj() {
  return request({
    url: '/rl/szhhdp/queryHhpj',
    method: 'post',
  })
}
// 河湖-河湖问题
export function queryHhwt() {
  return request({
    url: '/rl/szhhdp/queryHhwt',
    method: 'post',
  })
}
