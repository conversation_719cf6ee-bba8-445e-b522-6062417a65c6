import request from '@/utils/request'

// 查询雨量告警阈值列表
export function listRule(query) {
  return request({
    url: '/matrix/rule/list',
    method: 'get',
    params: query
  })
}

// 查询雨量告警阈值详细
export function getRule(sid) {
  return request({
    url: '/matrix/rule/' + sid,
    method: 'get'
  })
}

// 新增雨量告警阈值
export function addRule(data) {
  return request({
    url: '/matrix/rule',
    method: 'post',
    data: data
  })
}

// 修改雨量告警阈值
export function updateRule(data) {
  return request({
    url: '/matrix/rule',
    method: 'put',
    data: data
  })
}

// 删除雨量告警阈值
export function delRule(sid) {
  return request({
    url: '/matrix/rule/' + sid,
    method: 'delete'
  })
}
