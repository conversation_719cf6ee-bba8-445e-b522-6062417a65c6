<template>
  <div style="width: 100%;height: 100%">
    <div id="gdContainer" style="width:100%;height:100%"></div>
  </div>
</template>
<script>
// import AMapLoader from '@amap/amap-jsapi-loader';
export default {
  name: 'gdMap',
  props: {
    //中心点位置
    centerArr: {
      type: Array,
    },
    //轨迹线
    lineArr: {
      type: Array
    }
  },
  data() {
    return {
      marker:null,
    }
  },
  watch:{
    lineArr:{
      handler(val){
        setTimeout(() => {
          this.initMap()
        }, 50)
      },
      deep:true,
      immediate:true
    }
  },
  mounted() {
    // setTimeout(() => {
    //   this.initMap()
    // }, 50)

  },
  methods: {
    // 初始化地图
    initMap() {
      AMapLoader.load({
        key: "8d181b818e4fbab9b34258b158a11311",
        plugin: ['AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PolyEditor', 'AMap.CircleEditor'],
        // 默认高德 sdk 版本为 1.4.4
        v: '2.0'
      }).then((AMap)=>{
        var that = this
      this.map = new AMap.Map('gdContainer', {
        resizeEnable: true, // 窗口大小调整
        center: this.centerArr, // 中心
        zoom: 16,
        zooms:[8,16]//最大缩放范围
      })
      // 构造官方卫星、路网图层
      var satelliteLayer = new AMap.TileLayer.Satellite()
      var roadNetLayer = new AMap.TileLayer.RoadNet()
      // 批量添加图层
      this.map.add([satelliteLayer, roadNetLayer])

      // 添加工具栏
      this.map.plugin(['AMap.ToolBar', 'AMap.Scale'], () => {
        // 工具条
        const toolbar = new AMap.ToolBar()
        // 比例尺
        const scale = new AMap.Scale()
        this.map.addControl(toolbar)
        this.map.addControl(scale)
      })
      // 添加maker
      this.marker = new AMap.Marker({
        map: this.map,
        position: this.centerArr,
        icon: require('@/assets/images/person.png'),

      })
      this.initroad() //点击maker绘制轨迹
      setTimeout(() => {
        this.marker.moveAlong(this.lineArr, 500) // 动态描点(需要二次动态绘制轨迹开启)
      }, 100)
      })


      // maker点击事件
      // this.marker.on('click', function (e) {
      //   that.initroad() //点击maker绘制轨迹
      //   setTimeout(() => {
      //     this.moveAlong(this.lineArr, 700) // 动态描点(需要二次动态绘制轨迹开启)
      //   }, 300)
      // })
    },

    // 初始化轨迹
    initroad() {
      // 绘制还未经过的路线
      this.polyline = new AMap.Polyline({
        map: this.map,
        path: this.lineArr,
        showDir: true,
        strokeColor: '#28F', // 线颜色--蓝色
        strokeWeight: 6, // 线宽
        lineJoin: 'round' // 折线拐点的绘制样式

      })
      // 绘制路过了的轨迹
      var passedPolyline = new AMap.Polyline({
        map: this.map,
        strokeColor: '#AF5', // 线颜色-绿色
        strokeWeight: 6 // 线宽
      })
      this.marker.on('moving', e => {
        passedPolyline.setPath(e.passedPath)
      })
      this.map.setFitView() // 合适的视口
      this.map.setZoom(16)
      // this.map.setZooms(5,16)
    },
  }
}
</script>
<style lang="scss" scoped>
/**右下角高德地图图标*/
::v-deep .amap-logo {
  display: none;
  opacity: 0 !important;
}

::v-deep .amap-copyright {
  opacity: 0;
}
</style>
