import request from '@/utils/request'

// 查询水泵流量信息列表
export function listWpflow(query) {
  return request({
    url: '/matrix/wpflow/list',
    method: 'get',
    params: query
  })
}

// 查询水泵流量信息详细
export function getWpflow(id) {
  return request({
    url: '/matrix/wpflow/' + id,
    method: 'get'
  })
}

// 新增水泵流量信息
export function addWpflow(data) {
  return request({
    url: '/matrix/wpflow',
    method: 'post',
    data: data
  })
}

// 修改水泵流量信息
export function updateWpflow(data) {
  return request({
    url: '/matrix/wpflow',
    method: 'put',
    data: data
  })
}

// 删除水泵流量信息
export function delWpflow(id) {
  return request({
    url: '/matrix/wpflow/' + id,
    method: 'delete'
  })
}

export function latestQ(query) {
  return request({
    url: '/matrix/wpflow/latest_q',
    method: 'get',
    params: query
  })
}

/**
 * 查询水库基本信息
 * @param query
 * @returns {AxiosPromise}
 */
export function basInfo(query) {
  return request({
    url: '/matrix/wpflow/basinfo',
    method: 'get',
    params: query
  })
}

/**
 * 查询泵站出入库流量统计
 */
export function pumpStatistics(query) {
  return request({
    url: '/matrix/wpflow/pump_statistics',
    method: 'get',
    params: query
  })
}
