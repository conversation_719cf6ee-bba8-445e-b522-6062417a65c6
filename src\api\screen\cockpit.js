import request from '@/utils/request'

// 驾驶舱-泵站管理
export function pump_management() {
  return request({
    url: '/matrix/cockpit/pump_management',
    method: 'get',
  })
}

//驾驶舱-巡查检查-巡查完成率
export function patrol_check() {
  return request({
    url: '/matrix/cockpit/patrol_check',
    method: 'get',
  })
}

//驾驶舱-巡查检查-隐患待处置
export function patrol_check_dispose() {
  return request({
    url: '/matrix/cockpit/patrol_check_dispose',
    method: 'get',
  })
}

//驾驶舱-水资源管理
export function water_resource(startTime, endTime) {
  return request({
    url: '/matrix/cockpit/water_resource?startTime=' + startTime + '&endTime=' + endTime,
    method: 'get',
  })
}

//驾驶舱-告警信息
export function alarm_info() {
  return request({
    url: '/matrix/cockpit/alarm_info',
    method: 'get',
  })
}

//驾驶舱-水情监测
export function water_monitor(encd) {
  return request({
    url: '/matrix/cockpit/water_monitor?encd=' + encd,
    method: 'get',
  })
}

//驾驶舱-维修养护
export function repair_upkeep() {
  return request({
    url: '/matrix/cockpit/repair_upkeep',
    method: 'get',
  })
}
