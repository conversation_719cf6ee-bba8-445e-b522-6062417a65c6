import request from '@/utils/request'

// 查询河流管理列表
export function listRiverZibo(query) {
  return request({
    url: '/rl/riverZibo/list',
    method: 'get',
    params: query
  })
}
// 查询无棣指定河流管理列表
export function getBasRiverWudiList(query) {
  return request({
    url: '/rl/riverZibo/wudi/list',
    method: 'get',
    params: query
  })
}
// 查询河流详情列表
export function listRiver(query) {
  return request({
    url: '/rl/water/riverInfo/list',
    method: 'get',
    params: query
  })
}

// 查询河流管理详细
export function getRiverZibo(id) {
  return request({
    url: '/rl/riverZibo/' + id,
    method: 'get'
  })
}

// 新增河流管理
export function addRiverZibo(data) {
  return request({
    url: '/rl/riverZibo',
    method: 'post',
    data: data
  })
}

// 修改河流管理
export function updateRiverZibo(data) {
  return request({
    url: '/rl/riverZibo',
    method: 'put',
    data: data
  })
}

// 删除河流管理
export function delRiverZibo(id) {
  return request({
    url: '/rl/riverZibo/' + id,
    method: 'delete'
  })
}
