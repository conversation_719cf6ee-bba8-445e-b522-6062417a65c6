import request from '@/utils/request'

// 查询工程列表
export function projectList() {
  return request({
    url: '/matrix/project/info/project_list',
    method: 'get',
  })
}

export function userList() {
  return request({
    url: '/matrix/project/info/all_users',
    method: 'get',
  })
}

export function deptList() {
  return request({
    url: '/matrix/project/info/all_depts',
    method: 'get',
  })
}
export function employee(encd) {
  return request({
    url: '/matrix/plan/employee?encd='+encd,
    method: 'get'
  })
}
export function employeeByRole(encd) {
  return request({
    url: '/matrix/plan/employeeByRole?encd='+encd,
    method: 'get'
  })
}
