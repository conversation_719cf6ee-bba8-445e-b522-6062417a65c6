import request from '@/utils/request'

// 查询保护对象列表
export function listObject(query) {
  return request({
    url: '/matrix/basinfo/object/list',
    method: 'get',
    params: query
  })
}

// 查询保护对象详细
export function getObject(pid) {
  return request({
    url: '/matrix/basinfo/object/' + pid,
    method: 'get'
  })
}

// 新增保护对象
export function addObject(data) {
  return request({
    url: '/matrix/basinfo/object',
    method: 'post',
    data: data
  })
}

// 修改保护对象
export function updateObject(data) {
  return request({
    url: '/matrix/basinfo/object',
    method: 'put',
    data: data
  })
}

// 删除保护对象
export function delObject(pid) {
  return request({
    url: '/matrix/basinfo/object/' + pid,
    method: 'delete'
  })
}
