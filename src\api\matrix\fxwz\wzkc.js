import request from '@/utils/request'

// 列表
export function inventoryList(query) {
  return request({
    url: '/matrix/wzBinding/mtStoreSum',
    method: 'get',
    params: query
  })
}
// 查询物资列表
export function inventoryListWithMode(query) {
  return request({
    url: '/matrix/wzExistDetails/list',
    method: 'get',
    params: query
  })
}
// 列表单个详情
export function inventoryDetail(id) {
  return request({
    url: '/matrix/wzBinding/mtStoreSumInfo/' + id,
    method: 'get',
  })
}
// 仓库列表
export function warehouseList(params) {
  return request({
    url: '/matrix/wzWarehouse/list',
    method: 'get',
    params
  })
}
