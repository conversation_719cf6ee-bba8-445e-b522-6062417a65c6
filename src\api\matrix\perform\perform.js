import request from '@/utils/request'

// 查询履职信息列表
export function listPerform(query) {
  return request({
    url: '/matrix/perform/list',
    method: 'get',
    params: query
  })
}
// 查询履职树列表
export function listBook(query) {
  return request({
    url: '/matrix/performBook/list',
    method: 'get',
    params: query
  })
}
// // 查询履职信息详细
// export function getPerform(id) {
//   return request({
//     url: '/matrix/perform/detail/' + id,
//     method: 'get'
//   })
// }

// 保存履职信息
export function addPerform(data) {
  return request({
    url: '/matrix/perform/add',
    method: 'post',
    data: data
  })
}

// // 修改履职信息
// export function updatePerform(data) {
//   return request({
//     url: '/matrix/perform/update',
//     method: 'put',
//     data: data
//   })
// }

// // 删除履职信息
//   export function delPerform(id) {
//     return request({
//       url: '/matrix/perform/delete/' + id,
//       method: 'delete'
//     })
// }

export function getThreeList(params) {
  return request({
    url: '/matrix/zerz/getThree',
    method: 'get',
    params: params
  })
}