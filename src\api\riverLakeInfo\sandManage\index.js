import request from '@/utils/request'

// 查询采砂管理规划列表
export function listSand_planning(query) {
    return request({
        url: '/product/sandManage/sand_planning/list',
        method: 'get',
        params: query
    })
}

// 查询采砂管理规划详细
export function getSand_planning(id) {
    return request({
        url: '/product/sandManage/sand_planning/' + id,
        method: 'get'
    })
}

// 新增采砂管理规划
export function addSand_planning(data) {
    return request({
        url: '/product/sandManage/sand_planning',
        method: 'post',
        data: data
    })
}

// 修改采砂管理规划
export function updateSand_planning(data) {
    return request({
        url: '/product/sandManage/sand_planning',
        method: 'put',
        data: data
    })
}

// 删除采砂管理规划
export function delSand_planning(id) {
    return request({
        url: '/product/sandManage/sand_planning/' + id,
        method: 'delete'
    })
}

// 查询采砂许可管理列表
export function listSandPermitManag(query) {
  // console.log(query);
  return request({
      url: '/product/sandPermitManag/sandPermitManag/list',
      method: 'get',
      params: query
  })
}

// 查询采砂许可管理详细
export function getSandPermitManag(id) {
  return request({
      url: '/product/sandPermitManag/sandPermitManag/' + id,
      method: 'get'
  })
}

// 新增采砂许可管理
export function addSandPermitManag(data) {
  return request({
      url: '/product/sandPermitManag/sandPermitManag',
      method: 'post',
      data: data
  })
}

// 修改采砂许可管理
export function updateSandPermitManag(data) {
  return request({
      url: '/product/sandPermitManag/sandPermitManag',
      method: 'put',
      data: data
  })
}

// 删除采砂许可管理
export function delSandPermitManag(id) {
  return request({
      url: '/product/sandPermitManag/sandPermitManag/' + id,
      method: 'delete'
  })
}

// 查询涉砂类工程列表
export function listSandRelatedPro(query) {
  return request({
      url: '/product/sandRelatedPro/sandRelatedPro/list',
      method: 'get',
      params: query
  })
}

// 查询涉砂类工程详细
export function getSandRelatedPro(id) {
  return request({
      url: '/product/sandRelatedPro/sandRelatedPro/' + id,
      method: 'get'
  })
}

// 新增涉砂类工程
export function addSandRelatedPro(data) {
  return request({
      url: '/product/sandRelatedPro/sandRelatedPro',
      method: 'post',
      data: data
  })
}

// 修改涉砂类工程
export function updateSandRelatedPro(data) {
  return request({
      url: '/product/sandRelatedPro/sandRelatedPro',
      method: 'put',
      data: data
  })
}

// 删除涉砂类工程
export function delSandRelatedPro(id) {
  return request({
      url: '/product/sandRelatedPro/sandRelatedPro/' + id,
      method: 'delete'
  })
}
