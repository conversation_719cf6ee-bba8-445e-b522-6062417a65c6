import request from '@/utils/request'

//查询水库
export function listBasReservoir() {
  return request({
    url: '/rl/forecast/runoffCoefficient/selectReservoirOptions',
    method: 'get',
  })
}

//查询河流
export function getBaseRiver() {
  return request({
    url: '/rl/riverZibo/wudi/river',
    method: 'get',
  })
}

export function getBaseRiver1() {
  return request({
    url: '/rl/riverZibo/wudi/river1',
    method: 'get',
  })
}

// 获取水库详细信息
export function getReservoir(resId) {
  return request({
    url: '/rl/hydraulic/getDetail?resId='+resId,
    method: 'get',
  })
}

// 获取水闸详细信息
export function getSluice(query) {
  return request({
    url: '/rl/wudi/sluice/list',
    method: 'get',
    params: query
  })
}

// 获取工程测站信息
export function getStcd(resId) {
  return request({
    url: '/rl/hydraulic/getStcd?resId='+resId,
    method: 'get',
  })
}

// 修改水库工程管理
export function updateReservoirInfo(data) {
    return request({
        url: '/rl/water/reservoirInfo',
        method: 'put',
        data: data
    })
}

// 获取工程详情
export function getDetailEng(resId){
  return request({
    url: '/rl/hydraulic/getDetailEng?resId='+resId,
    method: 'get',
  })
}
//修改库站防洪
export function updateEng(data){
    return request({
        url: '/rl/water/rsvrfcch',
        method: 'put',
        data: data
    })
}

//查询划界确权界桩/界碑点位信息
export function billboardOrPins(params) {
  return request({
    url: "/rl/water/rsvdelimitation/list",
    method: "get",
    params: params
  })
}
