import request from '@/utils/request'

// 查询设备设施(水库)列表
export function listEquip(query) {
  return request({
    url: '/matrix/project/list',
    method: 'get',
    params: query
  })
}
// 查询设备设施(水库)列表  一新
export function listEquipNew(query) {
  return request({
    url: '/matrix/equip/list',
    method: 'get',
    params: query
  })
}

// 查询设备设施(水库)详细
export function getEquip(eid) {
  return request({
    url: '/matrix/equip/' + eid,
    method: 'get'
  })
}

// 新增设备设施(水库)
export function addEquip(data) {
  return request({
    url: '/matrix/equip',
    method: 'post',
    data: data
  })
}

// 修改设备设施(水库)
export function updateEquip(data) {
  return request({
    url: '/matrix/equip',
    method: 'put',
    data: data
  })
}

// 删除设备设施(水库)
export function delEquip(eid) {
  return request({
    url: '/matrix/equip/' + eid,
    method: 'delete'
  })
}
