define(["exports","./defaultValue-f6d5e6da"],(function(t,n){"use strict";var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var r=function(t){null==t&&(t=(new Date).getTime()),this.N=624,this.M=397,this.MATRIX_A=2567483615,this.UPPER_MASK=2147483648,this.LOWER_MASK=2147483647,this.mt=new Array(this.N),this.mti=this.N+1,t.constructor==Array?this.init_by_array(t,t.length):this.init_seed(t)};r.prototype.init_seed=function(t){for(this.mt[0]=t>>>0,this.mti=1;this.mti<this.N;this.mti++){t=this.mt[this.mti-1]^this.mt[this.mti-1]>>>30;this.mt[this.mti]=(1812433253*((4294901760&t)>>>16)<<16)+1812433253*(65535&t)+this.mti,this.mt[this.mti]>>>=0}},r.prototype.init_by_array=function(t,n){var i,e,r;for(this.init_seed(19650218),i=1,e=0,r=this.N>n?this.N:n;r;r--){var a=this.mt[i-1]^this.mt[i-1]>>>30;this.mt[i]=(this.mt[i]^(1664525*((4294901760&a)>>>16)<<16)+1664525*(65535&a))+t[e]+e,this.mt[i]>>>=0,e++,++i>=this.N&&(this.mt[0]=this.mt[this.N-1],i=1),e>=n&&(e=0)}for(r=this.N-1;r;r--){a=this.mt[i-1]^this.mt[i-1]>>>30;this.mt[i]=(this.mt[i]^(1566083941*((4294901760&a)>>>16)<<16)+1566083941*(65535&a))-i,this.mt[i]>>>=0,++i>=this.N&&(this.mt[0]=this.mt[this.N-1],i=1)}this.mt[0]=2147483648},r.prototype.random_int=function(){var t,n=new Array(0,this.MATRIX_A);if(this.mti>=this.N){var i;for(this.mti==this.N+1&&this.init_seed(5489),i=0;i<this.N-this.M;i++)t=this.mt[i]&this.UPPER_MASK|this.mt[i+1]&this.LOWER_MASK,this.mt[i]=this.mt[i+this.M]^t>>>1^n[1&t];for(;i<this.N-1;i++)t=this.mt[i]&this.UPPER_MASK|this.mt[i+1]&this.LOWER_MASK,this.mt[i]=this.mt[i+(this.M-this.N)]^t>>>1^n[1&t];t=this.mt[this.N-1]&this.UPPER_MASK|this.mt[0]&this.LOWER_MASK,this.mt[this.N-1]=this.mt[this.M-1]^t>>>1^n[1&t],this.mti=0}return t=this.mt[this.mti++],t^=t>>>11,t^=t<<7&2636928640,t^=t<<15&4022730752,(t^=t>>>18)>>>0},r.prototype.random_int31=function(){return this.random_int()>>>1},r.prototype.random_incl=function(){return this.random_int()*(1/4294967295)},r.prototype.random=function(){return this.random_int()*(1/4294967296)},r.prototype.random_excl=function(){return(this.random_int()+.5)*(1/4294967296)},r.prototype.random_long=function(){return(67108864*(this.random_int()>>>5)+(this.random_int()>>>6))*(1/9007199254740992)};var a=e(r);const o={EPSILON1:.1,EPSILON2:.01,EPSILON3:.001,EPSILON4:1e-4,EPSILON5:1e-5,EPSILON6:1e-6,EPSILON7:1e-7,EPSILON8:1e-8,EPSILON9:1e-9,EPSILON10:1e-10,EPSILON11:1e-11,EPSILON12:1e-12,EPSILON13:1e-13,EPSILON14:1e-14,EPSILON15:1e-15,EPSILON16:1e-16,EPSILON17:1e-17,EPSILON18:1e-18,EPSILON19:1e-19,EPSILON20:1e-20,EPSILON21:1e-21,GRAVITATIONALPARAMETER:3986004418e5,SOLAR_RADIUS:6955e5,LUNAR_RADIUS:1737400,SIXTY_FOUR_KILOBYTES:65536,FOUR_GIGABYTES:4294967296};o.sign=n.defaultValue(Math.sign,(function(t){return 0===(t=+t)||t!=t?t:t>0?1:-1})),o.signNotZero=function(t){return t<0?-1:1},o.toSNorm=function(t,i){return i=n.defaultValue(i,255),Math.round((.5*o.clamp(t,-1,1)+.5)*i)},o.fromSNorm=function(t,i){return i=n.defaultValue(i,255),o.clamp(t,0,i)/i*2-1},o.normalize=function(t,n,i){return 0===(i=Math.max(i-n,0))?0:o.clamp((t-n)/i,0,1)},o.sinh=n.defaultValue(Math.sinh,(function(t){return(Math.exp(t)-Math.exp(-t))/2})),o.cosh=n.defaultValue(Math.cosh,(function(t){return(Math.exp(t)+Math.exp(-t))/2})),o.lerp=function(t,n,i){return(1-i)*t+i*n},o.PI=Math.PI,o.ONE_OVER_PI=1/Math.PI,o.PI_OVER_TWO=Math.PI/2,o.PI_OVER_THREE=Math.PI/3,o.PI_OVER_FOUR=Math.PI/4,o.PI_OVER_SIX=Math.PI/6,o.THREE_PI_OVER_TWO=3*Math.PI/2,o.TWO_PI=2*Math.PI,o.ONE_OVER_TWO_PI=1/(2*Math.PI),o.RADIANS_PER_DEGREE=Math.PI/180,o.DEGREES_PER_RADIAN=180/Math.PI,o.RADIANS_PER_ARCSECOND=o.RADIANS_PER_DEGREE/3600,o.toRadians=function(t){return t*o.RADIANS_PER_DEGREE},o.toDegrees=function(t){return t*o.DEGREES_PER_RADIAN},o.convertLongitudeRange=function(t){const n=o.TWO_PI,i=t-Math.floor(t/n)*n;return i<-Math.PI?i+n:i>=Math.PI?i-n:i},o.clampToLatitudeRange=function(t){return o.clamp(t,-1*o.PI_OVER_TWO,o.PI_OVER_TWO)},o.negativePiToPi=function(t){return t>=-o.PI&&t<=o.PI?t:o.zeroToTwoPi(t+o.PI)-o.PI},o.zeroToTwoPi=function(t){if(t>=0&&t<=o.TWO_PI)return t;const n=o.mod(t,o.TWO_PI);return Math.abs(n)<o.EPSILON14&&Math.abs(t)>o.EPSILON14?o.TWO_PI:n},o.mod=function(t,n){return o.sign(t)===o.sign(n)&&Math.abs(t)<Math.abs(n)?t:(t%n+n)%n},o.equalsEpsilon=function(t,i,e,r){e=n.defaultValue(e,0),r=n.defaultValue(r,e);const a=Math.abs(t-i);return a<=r||a<=e*Math.max(Math.abs(t),Math.abs(i))},o.lessThan=function(t,n,i){return t-n<-i},o.lessThanOrEquals=function(t,n,i){return t-n<i},o.greaterThan=function(t,n,i){return t-n>i},o.greaterThanOrEquals=function(t,n,i){return t-n>-i};const s=[1];o.factorial=function(t){const n=s.length;if(t>=n){let i=s[n-1];for(let e=n;e<=t;e++){const t=i*e;s.push(t),i=t}}return s[t]},o.incrementWrap=function(t,i,e){return e=n.defaultValue(e,0),++t>i&&(t=e),t},o.isPowerOfTwo=function(t){return 0!==t&&0==(t&t-1)},o.nextPowerOfTwo=function(t){return--t,t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,t|=t>>16,++t},o.previousPowerOfTwo=function(t){return t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,t|=t>>16,t=((t|=t>>32)>>>0)-(t>>>1)},o.clamp=function(t,n,i){return t<n?n:t>i?i:t};let h=new a;o.setRandomNumberSeed=function(t){h=new a(t)},o.nextRandomNumber=function(){return h.random()},o.randomBetween=function(t,n){return o.nextRandomNumber()*(n-t)+t},o.acosClamped=function(t){return Math.acos(o.clamp(t,-1,1))},o.asinClamped=function(t){return Math.asin(o.clamp(t,-1,1))},o.chordLength=function(t,n){return 2*n*Math.sin(.5*t)},o.logBase=function(t,n){return Math.log(t)/Math.log(n)},o.cbrt=n.defaultValue(Math.cbrt,(function(t){const n=Math.pow(Math.abs(t),1/3);return t<0?-n:n})),o.log2=n.defaultValue(Math.log2,(function(t){return Math.log(t)*Math.LOG2E})),o.fog=function(t,n){const i=t*n;return 1-Math.exp(-i*i)},o.fastApproximateAtan=function(t){return t*(-.1784*Math.abs(t)-.0663*t*t+1.0301)},o.fastApproximateAtan2=function(t,n){let i,e=Math.abs(t);i=Math.abs(n);const r=Math.max(e,i);i=Math.min(e,i);const a=i/r;return e=o.fastApproximateAtan(a),e=Math.abs(n)>Math.abs(t)?o.PI_OVER_TWO-e:e,e=t<0?o.PI-e:e,e=n<0?-e:e,e};var u=o;t.CesiumMath=u,t.commonjsGlobal=i,t.getDefaultExportFromCjs=e}));
