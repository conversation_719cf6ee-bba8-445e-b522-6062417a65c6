import request from '@/utils/request'

// 查询淹没分析列表
export function listYmfx(query) {
  return request({
    url: '/matrix/ymfx/list',
    method: 'get',
    params: query
  })
}

// 查询淹没分析详细
export function getYmfx(id) {
  return request({
    url: '/matrix/ymfx/' + id,
    method: 'get'
  })
}

// 新增淹没分析
export function addYmfx(data) {
  return request({
    url: '/matrix/ymfx',
    method: 'post',
    data: data
  })
}

// 修改淹没分析
export function updateYmfx(data) {
  return request({
    url: '/matrix/ymfx',
    method: 'put',
    data: data
  })
}

// 删除淹没分析
export function delYmfx(id) {
  return request({
    url: '/matrix/ymfx/' + id,
    method: 'delete'
  })
}
// 淹没分析数据维护
export function listYubao(params) {
  return request({
    url: 'forecast/screen/yubao/listYubao',
    method: 'get',
    params
  })
}
