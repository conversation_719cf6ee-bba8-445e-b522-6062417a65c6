import request from '@/utils/request'

// 查询抢险队伍列表
export function listTeam(query) {
  return request({
    url: '/matrix/rescue-team/list',
    method: 'get',
    params: query
  })
}

// 查询抢险队伍详细
export function getTeam(rid) {
  return request({
    url: '/matrix/rescue-team/' + rid,
    method: 'get'
  })
}

// 新增抢险队伍
export function addTeam(data) {
  return request({
    url: '/matrix/rescue-team',
    method: 'post',
    data: data
  })
}

// 修改抢险队伍
export function updateTeam(data) {
  return request({
    url: '/matrix/rescue-team',
    method: 'put',
    data: data
  })
}

// 删除抢险队伍
export function delTeam(rid) {
  return request({
    url: '/matrix/rescue-team/' + rid,
    method: 'delete'
  })
}

//队伍人员
// 查询队伍人员比列表
export function listPerson(query) {
    return request({
      url: '/matrix/team-person/list',
      method: 'get',
      params: query
    })
  }

// 查询队伍人员比详细
export function getPerson(tid) {
  return request({
    url: '/matrix/team-person/' + tid,
    method: 'get'
  })
}

// 新增队伍人员比
export function addPerson(data) {
  return request({
    url: '/matrix/team-person',
    method: 'post',
    data: data
  })
}

// 修改队伍人员比
export function updatePerson(data) {
  return request({
    url: '/matrix/team-person',
    method: 'put',
    data: data
  })
}

// 删除队伍人员比
export function delPerson(tid) {
  return request({
    url: '/matrix/team-person/' + tid,
    method: 'delete'
  })
}

  //查询行政区划
export function getAreaSelect(query) {
    return request({
      url: '/matrix/section/areaSelect',
      method: 'get',
      params:query
    })
  }

//根据value值查询行政区划名字
export function getAreaName(query) {
  return request({
    url: '/matrix/section/areaName',
    method: 'get',
    params:query
  })
}

//四管-抢险队伍
export function rescue_team(query) {
  return request({
    url: '/matrix/baseline/siguan/rescue_team',
    method: 'get',
    params: query
  })
}