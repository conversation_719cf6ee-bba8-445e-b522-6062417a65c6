import request from '@/utils/request'

// 查询控制工程基本生态水量列表
export function listCtlvolume(query) {
  return request({
    url: '/product/ctlvolume/list',
    method: 'get',
    params: query
  })
}

// 查询控制工程基本生态水量详细
export function getCtlvolume(id) {
  return request({
    url: '/product/ctlvolume/' + id,
    method: 'get'
  })
}

// 新增控制工程基本生态水量
export function addCtlvolume(data) {
  return request({
    url: '/product/ctlvolume',
    method: 'post',
    data: data
  })
}

// 修改控制工程基本生态水量
export function updateCtlvolume(data) {
  return request({
    url: '/product/ctlvolume',
    method: 'put',
    data: data
  })
}

// 删除控制工程基本生态水量
export function delCtlvolume(id) {
  return request({
    url: '/product/ctlvolume/' + id,
    method: 'delete'
  })
}
