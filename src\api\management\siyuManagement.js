import request from "@/utils/request";
// 获取预报成果
export function schemeList(params) {
  return request({
    url: "/zhy-forecast/b/scheme/list",
    method: "get",
    params,
  });
}
// 泄洪/潮位 结果
export function listBackend(params) {
  return request({
    url: "/zhy-forecast/screen/preview/listBackend",
    method: "get",
    params,
  });
}
//预报成果地图信息
export function forecastResults(params) {
  return request({
    url: "/zhy-forecast/screen/preview/forecastResults",
    method: "get",
    params,
  });
}


// 成果评定列表
export function evaluationList(params) {
  return request({
    url: "/zhy-forecast/b/scheme/list",
    method: "get",
    params,
  });
}

// 水清变化趋势  潮位 水位
// schemeCode 方案编码
// sectionId 断面编码
// type 类型 1:潮位 2:流量
export function regimenChanges(params) {
  return request({
    url: `/zhy-forecast/b/scheme/getRegimenChanges/${params.schemeCode}/${params.sectionId}/${params.type}`,
    method: "get",
  });
}


// 获取断面列表
export function stBprpList(params) {
  return request({
    url: "/rainwater/b/stBprp/getList",
    method: "get",
    params,
  });
}


// 获取水情变化趋势
export function selectTrendWaterQuality(params) {
  return request({
    url: `/zhy-forecast/b/scheme/selectTrendWaterQuality/${params.schemeCode}/${params.sectionId}`,
    method: "get",
  });
}




//抢险专家列表
export function specList(params) {
  return request({
    url: "/zhy-forecast/b/spec/list",
    method: "get",
    params,
  });
}

export function specAdd(data) {
  return request({
    url: "/zhy-forecast/b/spec",
    method: "post",
    data,
  });
}
//抢险专家编辑
export function specEdit(data) {
  return request({
    url: "/zhy-forecast/b/spec",
    method: "put",
    data,
  });
}

//抢险专家删除
export function specDelete(id) {
  return request({
    url: "/zhy-forecast/b/spec/" + id,
    method: "delete",
  });
}






//预案管理列表
export function planList(params) {
  return request({
    url: "/zhy-forecast/b/preplan/list",
    method: "get",
    params,
  });
}

export function planAdd(data) {
  return request({
    url: "/zhy-forecast/b/preplan",
    method: "post",
    data,
  });
}
//预案管理编辑
export function planEdit(data) {
  return request({
    url: "/zhy-forecast/b/preplan",
    method: "put",
    data,
  });
}

//预案管理删除
export function planDelete(id) {
  return request({
    url: "/zhy-forecast/b/preplan/" + id,
    method: "delete",
  });
}


// 潮位预报成果导出
export function exportTideForecast(params) {
  return request({
    url: `/zhy-forecast/b/scheme/export/ForecastTide/${params.schemeCode}/${params.sectionId}`,
    method: "get",
    responseType: "blob",
    headers: {
      Accept:
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    },
  });
}


// 洪峰流量预报成果导出
export function exportFloodForecast(params) {
  return request({
    url: `/zhy-forecast/b/scheme/export/ForecastFlood/${params.schemeCode}/${params.sectionId}`,
    method: "get",
    responseType: "blob",
    headers: {
      'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }
  });
}


