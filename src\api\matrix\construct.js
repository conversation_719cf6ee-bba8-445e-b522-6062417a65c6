import request from '@/utils/request'

// 查询工程建设列表
export function listConstruct(query) {
  return request({
    url: '/matrix/file_manage/list',
    method: 'get',
    params: query
  })
}

// 查询工程建设详细
export function getConstruct(id) {
  return request({
    url: '/matrix/file_manage/info/' + id,
    method: 'get'
  })
}

// 新增工程建设
export function addConstruct(data) {
  return request({
    url: '/matrix/file_manage',
    method: 'post',
    data: data
  })
}

// 修改工程建设
export function updateConstruct(data) {
  return request({
    url: '/matrix/file_manage',
    method: 'put',
    data: data
  })
}

// 删除工程建设
export function delConstruct(id) {
  return request({
    url: '/matrix/file_manage/' + id,
    method: 'delete'
  })
}

// 查询VIEW列表
export function listProj(query) {
  return request({
    url: '/matrix/file_manage/list_proj',
    method: 'get',
    params: query
  })
}
