import request from '@/utils/request'

// 查询水库清违整治问题列表
export function listViolations(query) {
    return request({
        url: '/product/water/violations/list',
        method: 'get',
        params: query
    })
}

// 查询水库清违整治问题详细
export function getViolations(id) {
    return request({
        url: '/product/water/violations/' + id,
        method: 'get'
    })
}

// 新增水库清违整治问题

export function addViolations(data) {
    return request({
        url: '/product/water/violations',
        method: 'post',
        data: data
    })
}

// 修改水库清违整治问题

export function updateViolations(data) {
    return request({
        url: '/product/water/violations',
        method: 'put',
        data: data
    })
}

// 删除水库清违整治问题

export function delViolations(id) {
    return request({
        url: '/product/water/violations/' + id,
        method: 'delete'
    })
}
