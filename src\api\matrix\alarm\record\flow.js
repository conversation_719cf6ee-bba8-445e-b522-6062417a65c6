import request from '@/utils/request'

// 查询流量告警列表
export function listAlarm(query) {
  return request({
    url: '/matrix/flow_alarm/list',
    method: 'get',
    params: query
  })
}

// 查询流量告警详细
export function getAlarm(sid) {
  return request({
    url: '/matrix/flow_alarm/' + sid,
    method: 'get'
  })
}

// 新增流量告警
export function addAlarm(data) {
  return request({
    url: '/matrix/flow_alarm',
    method: 'post',
    data: data
  })
}

// 修改流量告警
export function updateAlarm(data) {
  return request({
    url: '/matrix/flow_alarm',
    method: 'put',
    data: data
  })
}
// 手动解除告警
export function handleEdit(data) {
  return request({
    url: '/matrix/flow_alarm/edit',
    method: 'put',
    data: data
  })
}
// 删除流量告警
export function delAlarm(sid) {
  return request({
    url: '/matrix/flow_alarm/' + sid,
    method: 'delete'
  })
}
