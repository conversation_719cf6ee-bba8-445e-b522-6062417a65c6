import request from '@/utils/request'

// 查询岸线规划填报列表
export function listPlanning(query) {
  return request({
    url: '/rl/shorelinePlanning/list',
    method: 'get',
    params: query
  })
}

// 查询岸线规划填报详细
export function getPlanning(id) {
  return request({
    url: '/rl/shorelinePlanning/' + id,
    method: 'get'
  })
}

// 新增岸线规划填报
export function addPlanning(data) {
  return request({
    url: '/rl/shorelinePlanning',
    method: 'post',
    data: data
  })
}

// 修改岸线规划填报
export function updatePlanning(data) {
  return request({
    url: '/rl/shorelinePlanning',
    method: 'put',
    data: data
  })
}

// 删除岸线规划填报
export function delPlanning(id) {
  return request({
    url: '/rl/shorelinePlanning/' + id,
    method: 'delete'
  })
}

// 查询所有河段数据
export function listAllHd() {
  return request({
    url: '/rl/hd/listAllHd',
    method: 'get',
  })
}

// 根据河段编码查询河段管理详细
export function getHdByHdbm(hdbm) {
  return request({
    url: '/rl/hd/getHdInfo/' + hdbm,
    method: 'get'
  })
}

// 查询所有正常使用的行政区域列表
export function listNormalArea() {
  return request({
      url: '/rl/water/area/listArea',
      method: 'get',
  })
}

// 查询文件列表，传入外键关联id
export function getFileList(id) {
  return request({
    url: '/rl/common/file/' + id,
    method: 'get'
  })
}

