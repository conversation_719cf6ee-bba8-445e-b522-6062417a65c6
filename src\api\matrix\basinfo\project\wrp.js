import request from '@/utils/request'

// 查询VIEW列表
export function listWrp(query) {
  return request({
    url: '/matrix/project/list',
    method: 'get',
    params: query
  })
}


// 查询VIEW列表
export function select_list(query) {
  return request({
    url: '/matrix/wrp/select_list',
    method: 'get',
    params: query
  })
}

// 查询VIEW列表
export function listProj(type) {
  return request({
    url: '/matrix//project/list',
    method: 'get',
    params: {type, pageSize: 999999999}
  })
}

// 查询VIEW详细
export function getWrp(bid) {
  return request({
    url: '/matrix/wrp/' + bid,
    method: 'get'
  })
}

// 新增VIEW
export function addWrp(data) {
  return request({
    url: '/matrix/wrp',
    method: 'post',
    data: data
  })
}

// 修改VIEW
export function updateWrp(data) {
  return request({
    url: '/matrix/wrp',
    method: 'put',
    data: data
  })
}

// 删除VIEW
export function delWrp(bid) {
  return request({
    url: '/matrix/wrp/' + bid,
    method: 'delete'
  })
}

//获取所有测站
export function listStation(query) {
  return request({
    url: "/matrix/station/list",
    method: "GET",
    params: query
  })
}

//获取所有测站
export function listStationAll(query) {
  return request({
    url: "/matrix/station/station_all",
    method: "GET",
    params: query
  })
}

//保存关联测站编号
export function saveSelectStation(data) {
  return request({
    url: "/matrix/wrp/saveStlist",
    method: "post",
    data: data
  })
}

//获取选中的测站
export function selectStation(query) {
  return request({
    url: "/matrix/wrp/stlist",
    method: "get",
    params: query
  })
}
