import request from '@/utils/request'

// 查询流量监测列表
export function listFlowdetection(query) {
  return request({
    url: '/matrix/flowdetection/list',
    method: 'get',
    params: query
  })
}
// 查询实时流量监测列表
export function realTimelistFlowdetection(query) {
  return request({
    url: '/matrix/flowdetection/llList',
    method: 'get',
    params: query
  })
}

//查询流量曲线
export function curveFlowdetection(query) {
  return request({
    url: '/matrix/flowdetection/curve',
    method: 'get',
    params: query
  })
}

// 查询流量监测详细
export function getFlowdetection(did) {
  return request({
    url: '/matrix/flowdetection/' + did,
    method: 'get'
  })
}

// 新增流量监测
export function addFlowdetection(data) {
  return request({
    url: '/matrix/flowdetection',
    method: 'post',
    data: data
  })
}

// 修改流量监测
export function updateFlowdetection(data) {
  return request({
    url: '/matrix/flowdetection',
    method: 'put',
    data: data
  })
}

// 删除流量监测
export function delFlowdetection(did) {
  return request({
    url: '/matrix/flowdetection/' + did,
    method: 'delete'
  })
}
// 获取监测类型
export function listDetection(data) {
  return request({
    url: '/matrix/flowdetection/listdtype',
    method: 'get',
    data: data
  })
}
