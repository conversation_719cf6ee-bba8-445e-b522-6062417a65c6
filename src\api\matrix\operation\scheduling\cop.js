import request from '@/utils/request'

// 查询控制运行计划列表
export function listCop(query) {
  return request({
    url: '/matrix/cop/list',
    method: 'get',
    params: query
  })
}

// 查询控制运行计划-水利工程列表
export function listCopWaterProjectWithPlan(query) {
  return request({
    url: '/matrix/cop/list_proj_plans',
    method: 'get',
    params: query
  })
}

// 查询控制运行计划详细
export function getCop(cid) {
  return request({
    url: '/matrix/cop/' + cid,
    method: 'get'
  })
}

// 新增控制运行计划
export function addCop(data) {
  return request({
    url: '/matrix/cop',
    method: 'post',
    data: data
  })
}

// 修改控制运行计划
export function updateCop(data) {
  return request({
    url: '/matrix/cop',
    method: 'put',
    data: data
  })
}

// 删除控制运行计划
export function delCop(cid) {
  return request({
    url: '/matrix/cop/' + cid,
    method: 'delete'
  })
}
