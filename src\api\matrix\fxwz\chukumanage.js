import request from '@/utils/request'

// 查询物资入库列表
export function listWzOutboundWh(query) {
  return request({
    url: '/matrix/WzOutboundWh/list',
    method: 'get',
    params: query
  })
}

// 查询物资入库详细
export function getWzOutboundWh(id) {
  return request({
    url: '/matrix/WzOutboundWh/' + id,
    method: 'get'
  })
}

// 新增物资出库
export function addWzOutboundWh(data) {
  return request({
    url: '/matrix/WzOutboundWh',
    method: 'post',
    data: data
  })
}

// 修改物资入库
export function updateWzOutboundWh(data) {
  return request({
    url: '/matrix/WzOutboundWh',
    method: 'put',
    data: data
  })
}

// 删除物资入库
export function delWzOutboundWh(id) {
  return request({
    url: '/matrix/WzOutboundWh/' + id,
    method: 'delete'
  })
}
// 仓库列表
export function warehouseList(params) {
  return request({
    url: '/matrix/wzWarehouse/list',
    method: 'get',
    params
  })
}
// 查询某个仓库下的物资
export function listWzBinding(query) {
  return request({
    url: '/matrix/wzBinding/list',
    method: 'get',
    params: query
  })
}
// 根据物资Id获取物资信息
export function materialList(id) {
  return request({
    url: '/matrix/wzBinding/mtInfoList/'+id,
    method: 'get',
  })
}
// 出库单
export function outBoundOrder(params) {
  return request({
    url: '/matrix/WzOutboundWh/outWarehouseList',
    method: 'get',
    params:params
  })
}
// 用户列表
export function getUserList(params) {
  return request({
    url: '/matrix/user/list2',
    method: 'get',
    params:params
  })
}
