import request from '@/utils/request'

// 查询界桩列列表
export function listBoundaryStone(query) {
  return request({
    url: '/rl/boundaryStone/list',
    method: 'get',
    params: query
  })
}

// 查询界桩详细列表
export function boundaryInfo(query) {
  return request({
    url: '/rl/boundaryStone/boundaryInfo',
    method: 'get',
    params: query
  })
}

//获取河流界桩
export function boundaryStone(rvcd) {
  return request({
    url: '/rl/boundaryStone/map?rvcd=' + rvcd,
    method: 'get'
  })
}

//新增范围线信息
export function addBoundaryStone(data) {
  return request({
    url: '/rl/boundaryStone',
    method: 'post',
    data
  })
}

//删除范围线信息
export function delBoundaryStone(id) {
  return request({
    url: '/rl/boundaryStone/'+ id,
    method: 'delete',
  })
}

//查询单个范围线信息
export function getOneBoundaryStone(id) {
  return request({
    url: '/rl/boundaryStone/'+ id,
    method: 'get',
  })
}

//更新范围线信息
export function updateBoundaryStone(data) {
  return request({
    url: '/rl/boundaryStone',
    method: 'put',
    data
  })
}

//新增界碑
export function addJb(data) {
  return request({
    url: '/rl/boundaryStone/addJb',
    method: 'post',
    data
  })
}

//删除界碑
export function delJb(id) {
  return request({
    url: '/rl/boundaryStone/delJb/'+ id,
    method: 'delete'
  })
}

//具体界碑信息
export function getOneJb(id) {
  return request({
    url: '/rl/boundaryStone/ckJb/' + id,
    method: 'get'
  })
}

// 更新界碑
export function updateJb(data) {
  return request({
    url: '/rl/boundaryStone/upJb',
    method: 'put',
    data
  })
}
