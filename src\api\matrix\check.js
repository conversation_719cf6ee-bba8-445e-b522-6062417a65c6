import request from '@/utils/request'

// 查询安全检查列表
export function listCheck(query) {
  return request({
    url: '/matrix/check/list',
    method: 'get',
    params: query
  })
}

// 查询安全检查详细
export function getCheck(id) {
  return request({
    url: '/matrix/check/exportInfo',
    method: 'get',
    params:{
      checkId:id
    }
  })
}

//新增安全检查
export function addCheck(data) {
  return request({
    url: '/matrix/check',
    method: 'post',
    data: data
  })
}

// 修改安全检查
export function updateCheck(data) {
  return request({
    url: '/matrix/check',
    method: 'put',
    data: data
  })
}

// 删除安全检查
export function delCheck(id) {
  return request({
    url: '/matrix/check/' + id,
    method: 'delete'
  })
}
