import request from '@/utils/request'

// 查询仓库管理列表
export function listByWid(query) {
    return request({
      url: '/matrix/wh/whManage/listByWid',
      method: 'get',
      params: query
    })
  }

//仓库列表
export function getAllWhSelect() {
    return request({
      url: '/matrix/wh/whManage/getAllWhSelect',
      method: 'get',
    })
}

//物资详情
export function getMtList(query) {
    return request({
      url: '/matrix/wh/mtManage/list',
      method: 'get',
      params:query
    })
}