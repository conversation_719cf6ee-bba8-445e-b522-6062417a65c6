import request from '@/utils/request'

// 查询党建文化列表
export function listpartyBuild(query) {
  return request({
    url: '/matrix/partyBuild/list',
    method: 'get',
    params: query
  })
}

// 查询党建文化详细
export function getpartyBuild(id) {
  return request({
    url: '/matrix/partyBuild/detail/' + id,
    method: 'get'
  })
}

// 新增党建文化
export function addpartyBuild(data) {
  return request({
    url: '/matrix/partyBuild/add',
    method: 'post',
    data: data
  })
}

// 修改党建文化
export function updatepartyBuild(data) {
  return request({
    url: '/matrix/partyBuild/update',
    method: 'put',
    data: data
  })
}

// 删除党建文化
  export function delpartyBuild(id) {
    return request({
      url: '/matrix/partyBuild/delete/' + id,
      method: 'delete'
    })
}
