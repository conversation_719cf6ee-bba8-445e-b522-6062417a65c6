import request from '@/utils/request'

// 查询水泵信息列表
export function listDevice(query) {
  return request({
    url: '/matrix/device/list',
    method: 'get',
    params: query
  })
}

// 查询水泵信息详细
export function getDevice(id) {
  return request({
    url: '/matrix/device/' + id,
    method: 'get'
  })
}

// 新增水泵信息
export function addDevice(data) {
  return request({
    url: '/matrix/device',
    method: 'post',
    data: data
  })
}

// 修改水泵信息
export function updateDevice(data) {
  return request({
    url: '/matrix/device',
    method: 'put',
    data: data
  })
}

// 删除水泵信息
export function delDevice(id) {
  return request({
    url: '/matrix/device/' + id,
    method: 'delete'
  })
}
/**
 * 查询工程责任人列表
 */
export function queryBzxxFzrList() {
  return request({
    url: '/matrix/device/queryBzxxFzrList',
    method: 'get'
  })
}

/**
 * 查询工程责任人详情
 */
export function queryBzxxFzrDetail(id) {
  return request({
    url: '/matrix/device/queryBzxxFzrList',
    method: 'get',
    data: id
  })
}
/**
 * 导出工程责任人列表
 */
export function exportBzxxFzrList(id) {
  return request({
    url: '/matrix/device/exportBzxxFzrList',
    method: 'post'
  })
}

/**
 * 新增工程责任人
 */
export function addBzxxFzr(formInfo) {
  return request({
    url: '/matrix/device/addBzxxFzr',
    method: 'post',
    data:formInfo
  })
}

/**
 * 修改工程责任人
 */
export function editBzxxFzr(formInfo) {
  return request({
    url: '/matrix/device/editBzxxFzr',
    method: 'put',
    data:formInfo
  })
}
/**
 * 删除工程责任人
 */
export function delBzxxFzr(id) {
  return request({
    url: '/matrix/device/delBzxxFzr/'+id,
    method: 'delete'
  })
}
