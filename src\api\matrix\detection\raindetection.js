import request from '@/utils/request'

// 查询降水量列表
export function listRaindetection(query) {
  return request({
    url: '/matrix/raindetection/list',
    method: 'get',
    params: query
  })
}

// 查询降水量详细
export function getRaindetection(stcd) {
  return request({
    url: '/matrix/raindetection/' + stcd,
    method: 'get'
  })
}

// 查询最大日降雨量
export function getMaxByDay(query) {
  return request({
    url: '/matrix/raindetection/listmaxday',
    method: 'get',
    params: query
  })
}
// 新增降水量
export function addRaindetection(data) {
  return request({
    url: '/matrix/raindetection',
    method: 'post',
    data: data
  })
}

// 修改降水量
export function updateRaindetection(data) {
  return request({
    url: '/matrix/raindetection',
    method: 'put',
    data: data
  })
}

// 删除降水量
export function delRaindetection(stcd) {
  return request({
    url: '/matrix/raindetection/' + stcd,
    method: 'delete'
  })
}

// 站点远程搜索
export function listRainSite(query) {
  return request({
    url: '/matrix/raindetection/listsite',
    method: 'get',
    params: query
  })
}
// 按天查询
export function listdataByDay(query) {
  return request({
    url: '/matrix/raindetection/listday',
    method: 'get',
    params: query
  })
}
// 取出最近一条数据
export function getRecently(query) {
  return request({
    url: '/matrix/raindetection/recently',
    method: 'get',
    params: query
  })
}
// 取出图标的最大值平均值以及累计值
export function getTopchars(query) {
  return request({
    url: '/matrix/raindetection/topchars',
    method: 'get',
    params: query
  })
}
// 查询瞬时降雨强度
export function selectCharsList(query) {
  return request({
    url: '/matrix/raindetection/listchars',
    method: 'get',
    params: query
  })
}
//查询实时降雨强度
export function selectRainFall(query) {
  return request({
    url: '/matrix/raindetection/listrainfall',
    method: 'get',
    params: query
  })
}
