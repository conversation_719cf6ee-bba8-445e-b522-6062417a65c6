import request from '@/utils/request'

// 查询安全告警规则列表
export function listRule(query) {
  return request({
    url: '/matrix/secure/rule/list',
    method: 'get',
    params: query
  })
}

// 查询安全告警规则详细
export function getRule(sid) {
  return request({
    url: '/matrix/secure/rule/' + sid,
    method: 'get'
  })
}

// 新增安全告警规则
export function addRule(data) {
  return request({
    url: '/matrix/secure/rule',
    method: 'post',
    data: data
  })
}

// 修改安全告警规则
export function updateRule(data) {
  return request({
    url: '/matrix/secure/rule',
    method: 'put',
    data: data
  })
}

// 删除安全告警规则
export function delRule(sid) {
  return request({
    url: '/matrix/secure/rule/' + sid,
    method: 'delete'
  })
}
// 获取位移测站列表
export function listGnssStation(query){
  return request({
    url: '/matrix/drift/liststation',
    method: 'get',
    params: query
  })
}
// 获取渗压测站列表
export function listOsmStation(query){
  return request({
    url: '/matrix/osmometer/liststation',
    method: 'get',
    params: query
  })
}
