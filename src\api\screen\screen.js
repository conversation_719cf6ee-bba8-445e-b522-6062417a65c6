import request from '@/utils/request'

// 首页-水库信息、安全鉴定查询接口
export function rsr_info(encd) {
  return request({
    url: '/matrix/screen/rsr_info?encd=' + encd,
    method: 'get',
  })
}

//四制-四个责任人
export function reservoid_duty_person(encd) {
  return request({
    url: '/matrix/screen/reservoid_duty_person?encd=' + encd,
    method: 'get',
  })
}
//四全-实时水情
export function real_rsvr(encd) {
  return request({
    url: '/matrix/screen/real_rsvr?encd=' + encd,
    method: 'get',
  })
}
//四全-实时雨情
export function real_pptn(query) {
  return request({
    url: '/matrix/screen/real_pptn',
    method: 'get',
    params: query
  })
}

//四全-巡查统计
export function patrol_statistics(encd) {
  return request({
    url: '/matrix/screen/patrol_statistics?encd=' + encd,
    method: 'get',
  })
}
//四管-安全鉴定
export function appraisal(encd) {
  return request({
    url: '/matrix/screen/appraisal?encd=' + encd,
    method: 'get',
  })
}
//四管-安全管理
export function secure_manage(encd) {
  return request({
    url: '/matrix/screen/secure_manage?encd=' + encd,
    method: 'get',
  })
}
//四管-维修养护
export function repair(encd) {
  return request({
    url: '/matrix/screen/repair?encd=' + encd,
    method: 'get',
  })
}
//四管-物资仓库（队伍）
export function rescue_team(encd) {
  return request({
    url: '/matrix/screen/rescue_team?encd=' + encd,
    method: 'get',
  })
}

//四管-物资仓库（物资）
export function mg_wh(encd) {
  return request({
    url: '/matrix/screen/mg_wh?encd=' + encd,
    method: 'get',
  })
}

//首页实时水位（地图弹窗）
export function real_waterlevel(query) {
  return request({
    url: '/matrix/baseline/siguan/real_waterlevel',
    method: 'get',
    params: query
  })
}

//首页实时出库流量（地图弹窗）
export function real_outflow(query) {
  return request({
    url: '/matrix/baseline/siguan/real_outflow',
    method: 'get',
    params: query
  })
}

//首页实时入库流量（地图弹窗）
export function real_inflow(query) {
  return request({
    url: '/matrix/baseline/siguan/real_inflow',
    method: 'get',
    params: query
  })
}
