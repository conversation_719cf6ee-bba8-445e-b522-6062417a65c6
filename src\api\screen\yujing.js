import request from '@/utils/request'

export function realWate(encd) {
  return request({
    url: '/hydmodel/alarm/real_water?encd=' + encd,
    method: 'get',
  })
}
export function realSecure(encd) {
  return request({
    url: '/hydmodel/alarm/real_secure?encd=' + encd,
    method: 'get',
  })
}
export function earlyWater(params) {
  return request({
    url: '/hydmodel/alarm/early_water',
    method: 'get',
    params
  })
}
export function earlyRegimen(params) {
  return request({
    url: '/hydmodel/alarm/early_regimen',
    method: 'get',
    params
  })
}
export function rainfallInfo(params) {
  return request({
    url: '/hydmodel/plan/rainfall_info',
    method: 'get',
    params
  })
}
export function emergencySupport(params) {
  return request({
    url: '/hydmodel/plan/emergency_support',
    method: 'get',
    params
  })
}
export function emergency(params) {
  return request({
    url: '/hydmodel/plan/emergency',
    method: 'get',
    params
  })
}
export function maxCellData(params) {
  return request({
    url: '/hydmodel/flood_routing_data/max_cell_data',
    method: 'get',
    params
  })
}
export function getOrganizationalGuarantee(params) {
  return request({
    url: '/hydmodel/plan/getOrganizationalGuarantee',
    method: 'get',
    params
  })
}
export function getMaterialSupport(params) {
  return request({
    url: '/hydmodel/plan/getMaterialSupport',
    method: 'get',
    params
  })
}
export function getTeamGuarantee(params) {
  return request({
    url: '/hydmodel/plan/getTeamGuarantee',
    method: 'get',
    params
  })
}
export function getExpertGuarantee(params) {
  return request({
    url: '/hydmodel/plan/getExpertGuarantee',
    method: 'get',
    params
  })
}
export function planStatistics(params) {
  return request({
    url: '/hydmodel/plan/statistics',
    method: 'get',
    params
  })
}
export function rainFlow(params) {
  return request({
    url: '/hydmodel/plan/rain_flow',
    method: 'get',
    params
  })
}
export function waterInfo(params) {
  return request({
    url: '/hydmodel/plan/water_info',
    method: 'get',
    params
  })
}
export function orgTree(params) {
  return request({
    url: '/hydmodel/alarm/orgTree',
    method: 'get',
    params
  })
}
export function getPlanGeneration(params) {
  return request({
    url: '/hydmodel/plan/planGeneration',
    method: 'get',
    params
  })
}
export function pointRain(params) {
  return request({
    url: '/hydmodel/plan/point_rain',
    method: 'get',
    params
  })
}
export function targetUser(data) {
  return request({
    url: '/hydmodel/alarm/publish/targetUser',
    method: 'post',
    data
  })
}
export function alarmInfo(params) {
  return request({
    url: '/hydmodel/plan/alarm_info',
    method: 'get',
    params
  })
}
