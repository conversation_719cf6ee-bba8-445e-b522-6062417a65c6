<template>
  <div ref="chart" :style="{ width: '100%', height: '100%' }"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'chartsView',
  props: {
    options: {
      type: Object,
      required: true,
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
  },
  data () {
    return {
      chartInstance: null,
    };
  },
  watch: {
    options: {
      handler (newVal, oldVal) {
        if (this.chartInstance) {
          this.chartInstance.setOption(newVal);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart();
    })
    if (this.autoResize) {
      window.addEventListener('resize', this.handleResize);
    }
  },
  beforeDestroy () {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  methods: {
    initChart () {
      this.chartInstance = echarts.init(this.$refs.chart);
      this.chartInstance.setOption(this.options);
    },
    handleResize () {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
