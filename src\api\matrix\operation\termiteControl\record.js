import request from '@/utils/request'

// 查询白蚁防治记录列表
export function listRecord(query) {
  return request({
    url: '/matrix/termiteControl/list',
    method: 'get', params: query
  })
}

// 查询白蚁防治记录详细
export function getRecord(id) {
  return request({
    url: '/matrix/termiteControl/' + id,
    method: 'get'
  })
}

// 新增白蚁防治记录
export function addRecord(data) {
  return request({
    url: '/matrix/termiteControl',
    method: 'post',
    data: data
  })
}

// 修改白蚁防治记录
export function updateRecord(data) {
  return request({
    url: '/matrix/termiteControl',
    method: 'put',
    data: data
  })
}

// 删除白蚁防治记录
export function delRecord(id) {
  return request({
    url: '/matrix/termiteControl/' + id,
    method: 'delete'
  })
}

//根据记录表id查询明细
export function queryByRecordId(recordId) {
  return request({
    url: '/matrix/termiteControl/queryDetailByRecordId',
    method: 'get',
    params: recordId
  })
}
