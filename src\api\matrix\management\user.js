import request from '@/utils/request'

// 查询组织机构用户列表
export function listUser(query) {
  return request({
    url: '/matrix/org_user/list',
    method: 'get',
    params: query
  })
}

// 查询组织机构用户详细
export function getUser(uid) {
  return request({
    url: '/matrix/org_user/' + uid,
    method: 'get'
  })
}

// 新增组织机构用户
export function addUser(data) {
  return request({
    url: '/matrix/org_user',
    method: 'post',
    data: data
  })
}

// 修改组织机构用户
export function updateUser(data) {
  return request({
    url: '/matrix/org_user',
    method: 'put',
    data: data
  })
}

// 删除组织机构用户
export function delUser(uid) {
  return request({
    url: '/matrix/org_user/' + uid,
    method: 'delete'
  })
}
