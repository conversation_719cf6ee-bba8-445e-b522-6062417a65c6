import request from '@/utils/request'

//四制-组织机构
export function org_list() {
  return request({
    url: '/matrix/sizhi/org_list',
    method: 'get',
  })
}

//四制-法律法规
export function laws() {
  return request({
    url: '/matrix/sizhi/laws',
    method: 'get',
  })
}

//四制-水库责任人
export function dutys() {
  return request({
    url: '/matrix/sizhi/dutys',
    method: 'get',
  })
}

//四制-岗位职责
export function org_duty(oid) {
  return request({
    url: '/matrix/sizhi/org_duty?oid=' + oid,
    method: 'get',
  })
}

// 四制-制度规范
export function system() {
  return request({
    url: '/matrix/sizhi/system',
    method: 'get',
  })
}

//四制-体制-管理主体
export function getManagementUnit(encd) {
  return request({
    url: '/matrix/tizhi/getManagementUnit?encd=' + encd,
    method: 'get',
  })
}

//四制-体制-产权/注册登记证
export function getCertificate(encd) {
  return request({
    url: '/matrix/tizhi/getCertificate?encd=' + encd,
    method: 'get',
  })
}

//四制-体制-考核评价评价指标
export function getEval(encd) {
  return request({
    url: '/matrix/tizhi/getEval?encd=' + encd,
    method: 'get',
  })
}

//四制-体制-职能批复
export function getApproval(encd) {
  return request({
    url: '/matrix/approval/list?encd=' + encd,
    method: 'get',
  })
}

//四制-机制-联动机构
export function getOrgList() {
  return request({
    url: '/matrix/sizhi/org_list',
    method: 'get',
  })
}

//四制-机制-根据组织机构查询职责
export function getOrgDuty(oid) {
  return request({
    url: '/matrix/sizhi/org_duty?oid=' + oid,
    method: 'get',
  })
}

//四制-机制-中间组织人员图
export function getOrgListNumber(singleEncd) {
  return request({
    url: '/matrix/jizhi/orgListNumber?encd=' + singleEncd,
    method: 'get',
  })
}

//四制-机制-根据组织机构查询人员
export function getOrgUser(oid) {
  return request({
    url: '/matrix/sizhi/org_user?oid=' + oid,
    method: 'get',
  })
}

//四制-机制-培训记录
export function getTrain(encd) {
  return request({
    url: '/matrix/jizhi/train?encd=' + encd,
    method: 'get',
  })
}

//四制-机制-党建文化
export function getPartyBuild(encd) {
  return request({
    url: '/matrix/partyBuild/list?encd=' + encd,
    method: 'get',
  })
}

//四制-机制-水文化
export function getWaterBuild(encd) {
  return request({
    url: '/matrix/waterBuild/list?encd=' + encd,
    method: 'get',
  })
}

//四制-法制-政策文件
export function getLawsAll(encd) {
  return request({
    url: '/matrix/sizhi/laws?encd=' + encd,
    method: 'get',
  })
}

export function getLaws(type) {
  return request({
    url: '/matrix/sizhi/laws?type=' + type,
    method: 'get',
  })
}

//四制-法制-执法队伍/监察证书
export function getLawTeam(encd) {
  return request({
    url: '/matrix/faZhi/getLawTeam?encd=' + encd,
    method: 'get',
  })
}

//四制-法制-执法类型
//暂无

//四制-责任制-三个责任人
export function getThreePerson(encd) {
  return request({
    url: '/matrix/zerz/getThree?encd=' + encd,
    method: 'get',
  })
}

//四制-责任制-履职要点
export function getPoint(encd) {
  return request({
    url: '/matrix/zerz/getPoint?encd=' + encd,
    method: 'get',
  })
}

//四制-责任制-工作职责
export function getPerform(encd) {
  return request({
    url: '/matrix/zerz/getPerform?encd=' + encd,
    method: 'get',
  })
}

//四制-责任制-履职培训
export function getPerTrain(encd) {
  return request({
    url: '/matrix/zerz/perTrain?encd=' + encd,
    method: 'get',
  })
}

//四制-机制-防汛物资
export function getMaterial(encd) {
  return request({
    url: '/matrix/jizhi/material?encd=' + encd,
    method: 'get',
  })
}

//四制-机制-管理设备设施
export function getEquipment(encd) {
  return request({
    url: '/matrix/jizhi/equipment?encd=' + encd,
    method: 'get',
  })
}

