import request from '@/utils/request'

// 查询仓库管理列表
export function applicationList(query) {
  return request({
    url: '/matrix/application/list',
    method: 'get',
    params: query
  })
}
export function recordreview(data) {
  return request({
    url: '/matrix/review-record/review',
    method: 'post',
    data
  })
}
export function recordhistory(query) {
  return request({
    url: '/matrix/review-record/history',
    method: 'get',
    params: query
  })
}
export function listReview(query) {
  return request({
    url: '/matrix/review/list',
    method: 'get',
    params: query
  })
}
export function reApplication(data) {
  return request({
    url: '/matrix/application/reApplication',
    method: 'post',
    data
  })
}
