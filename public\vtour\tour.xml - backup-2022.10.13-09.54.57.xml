﻿<krpano version="1.19" title="Virtual Tour">

	<include url="skin/vtourskin.xml" />

	<!-- customize skin settings: maps, gyro, webvr, thumbnails, tooltips, layout, design, ... -->
	<skin_settings maps="true"
	               maps_type="google"
	               maps_bing_api_key=""
	               maps_google_api_key=""
	               maps_zoombuttons="false"
	               gyro="true"
	               webvr="true"
	               webvr_gyro_keeplookingdirection="false"
	               webvr_prev_next_hotspots="true"
	               littleplanetintro="false"
	               title="true"
	               thumbs="true"
	               thumbs_width="120" thumbs_height="80" thumbs_padding="10" thumbs_crop="0|40|240|160"
	               thumbs_opened="false"
	               thumbs_text="false"
	               thumbs_dragging="true"
	               thumbs_onhoverscrolling="false"
	               thumbs_scrollbuttons="false"
	               thumbs_scrollindicator="false"
	               thumbs_loop="false"
	               tooltips_buttons="false"
	               tooltips_thumbs="false"
	               tooltips_hotspots="false"
	               tooltips_mapspots="false"
	               deeplinking="false"
	               loadscene_flags="MERGE"
	               loadscene_blend="OPENBLEND(0.5, 0.0, 0.75, 0.05, linear)"
	               loadscene_blend_prev="SLIDEBLEND(0.5, 180, 0.75, linear)"
	               loadscene_blend_next="SLIDEBLEND(0.5,   0, 0.75, linear)"
	               loadingtext="loading..."
	               layout_width="100%"
	               layout_maxwidth="814"
	               controlbar_width="-24"
	               controlbar_height="40"
	               controlbar_offset="20"
	               controlbar_offset_closed="-40"
	               controlbar_overlap.no-fractionalscaling="10"
	               controlbar_overlap.fractionalscaling="0"
	               design_skin_images="vtourskin.png"
	               design_bgcolor="0x2D3E50"
	               design_bgalpha="0.8"
	               design_bgborder="0"
	               design_bgroundedge="1"
	               design_bgshadow="0 4 10 0x000000 0.3"
	               design_thumbborder_bgborder="3 0xFFFFFF 1.0"
	               design_thumbborder_padding="2"
	               design_thumbborder_bgroundedge="0"
	               design_text_css="color:#FFFFFF; font-family:Arial;"
	               design_text_shadow="1"
	               />

	<!--
	    For an alternative skin design either change the <skin_settings> values 
	    from above or optionally include one of the predefined designs from below.
	-->
	<!-- <include url="skin/vtourskin_design_flat_light.xml"  /> -->
	<!-- <include url="skin/vtourskin_design_glass.xml"       /> -->
	<!-- <include url="skin/vtourskin_design_ultra_light.xml" /> -->
	<!-- <include url="skin/vtourskin_design_117.xml"         /> -->
	<!-- <include url="skin/vtourskin_design_117round.xml"    /> -->
	<!-- <include url="skin/vtourskin_design_black.xml"       /> -->


	<!-- startup action - load the first scene -->
	<action name="startup" autorun="onstart">
		if(startscene === null OR !scene[get(startscene)], copy(startscene,scene[0].name); );
		loadscene(get(startscene), null, MERGE);
		if(startactions !== null, startactions() );
	</action>


	
	<scene name="scene_0001" title="库中央鸟瞰" onstart="" havevrimage="true" thumburl="panos/0001.tiles/thumb.jpg" lat="35.44469297" lng="117.96921194" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0001.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0001.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0001.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0001.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0001.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0002" title="坝前坡" onstart="" havevrimage="true" thumburl="panos/0002.tiles/thumb.jpg" lat="35.43259447" lng="117.96987114" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0002.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0002.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0002.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0002.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0002.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0003" title="坝后坡" onstart="" havevrimage="true" thumburl="panos/0003.tiles/thumb.jpg" lat="35.43106997" lng="117.97177136" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0003.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0003.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0003.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0003.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0003.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0004" title="溢洪闸前" onstart="" havevrimage="true" thumburl="panos/0004.tiles/thumb.jpg" lat="35.43419569" lng="117.96514358" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0004.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0004.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0004.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0004.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0004.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0005" title="溢洪闸后" onstart="" havevrimage="true" thumburl="panos/0005.tiles/thumb.jpg" lat="35.43306722" lng="117.96467494" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0005.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0005.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0005.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0005.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0005.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0006" title="东岭水文站" onstart="" havevrimage="true" thumburl="panos/0006.tiles/thumb.jpg" lat="35.39869858" lng="117.96096531" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0006.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0006.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0006.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0006.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0006.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0007" title="橡胶坝" onstart="" havevrimage="true" thumburl="panos/0007.tiles/thumb.jpg" lat="35.39438011" lng="117.96157456" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0007.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0007.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0007.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0007.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0007.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0008" title="汇流口一" onstart="" havevrimage="true" thumburl="panos/0008.tiles/thumb.jpg" lat="35.37969622" lng="117.96088303" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0008.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0008.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0008.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0008.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0008.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>

	<scene name="scene_0009" title="汇流口二" onstart="" havevrimage="true" thumburl="panos/0009.tiles/thumb.jpg" lat="35.36060989" lng="117.97061114" heading="0.0">

		<view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />

		<preview url="panos/0009.tiles/preview.jpg" />

		<image type="CUBE" prealign="0|0.0|0" multires="true" tilesize="512" if="!webvr.isenabled">
			<level tiledimagewidth="2624" tiledimageheight="2624">
				<cube url="panos/0009.tiles/%s/l3/%v/l3_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="1280" tiledimageheight="1280">
				<cube url="panos/0009.tiles/%s/l2/%v/l2_%s_%v_%h.jpg" />
			</level>
			<level tiledimagewidth="640" tiledimageheight="640">
				<cube url="panos/0009.tiles/%s/l1/%v/l1_%s_%v_%h.jpg" />
			</level>
		</image>

		<image prealign="0|0.0|0" if="webvr.isenabled">
			<cube url="panos/0009.tiles/vr/pano_%s.jpg" />
		</image>

	</scene>


</krpano>
