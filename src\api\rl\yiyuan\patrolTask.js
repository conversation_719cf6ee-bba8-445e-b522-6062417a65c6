import request from '@/utils/request'

// 查询巡河记录列表
export function listPatrolTask(query) {
  return request({
    url: '/rl/patrolTask/list',
    method: 'get',
    params: query
  })
}

// 查询巡河记录详细
export function getPatrolTask(id) {
  return request({
    url: '/rl/patrolTask/' + id,
    method: 'get'
  })
}

// 新增巡河记录
export function addPatrolTask(data) {
  return request({
    url: '/rl/patrolTask',
    method: 'post',
    data: data
  })
}

// 修改巡河记录
export function updatePatrolTask(data) {
  return request({
    url: '/rl/patrolTask',
    method: 'put',
    data: data
  })
}

// 删除巡河记录
export function delPatrolTask(id) {
  return request({
    url: '/rl/patrolTask/' + id,
    method: 'delete'
  })
}
//  巡河轨迹
export function getPatrolTrace(id) {
  return request({
    url: '/rl/patrolTask/getPatrolTrace?id=' + id,
    method: 'get'
  })
}
