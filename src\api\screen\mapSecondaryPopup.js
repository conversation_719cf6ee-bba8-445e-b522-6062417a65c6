import request from '@/utils/request'

// 水库弹窗-水库信息
export function getReserviorDetail(rscd) {
  return request({
    url: '/matrix/popup/getReserviorDetail?rscd=' + rscd,
    method: 'get',
  })
}

// 水库弹窗-水位过程线
export function getWaterLine(query) {
  return request({
    url: '/matrix/popup/waterLine',
    method: 'get',
    params: query
  })
}

// 水库弹窗-雨量过程线
export function getRainLine(query) {
  return request({
    url: '/matrix/popup/rainLine',
    method: 'get',
    params: query
  })
}

//水库弹窗-承纳分析
export function getAnalysis(rscd) {
  return request({
    url: '/matrix/popup/getAnalysis?rscd=' + rscd,
    method: 'get',
  })
}

//水库弹窗-库容曲线
export function getZavarl(rscd) {
  return request({
    url: '/matrix/popup/getZavarl?rscd=' + rscd,
    method: 'get',
  })
}

//根据库容查询水位
export function getwlByCap(rscd, capt) {
  return request({
    url: '/matrix/popup/getwlByCap?rscd=' + rscd + '&capt=' + capt,
    method: 'get',
  })
}

//位移弹窗-位移过程
export function getGnssData(query) {
  return request({
    url: '/matrix/popup/getGnssData',
    method: 'get',
    params: query
  })
}

//位移弹窗-位移分析
export function getGnssAnalyse(query) {
  return request({
    url: '/matrix/popup/getGnssAnalyse',
    method: 'get',
    params: query
  })
}

//渗压弹窗
export function getOsmo(query) {
  return request({
    url: '/matrix/popup/getOsmo',
    method: 'get',
    params: query
  })
}
//三个责任人
export function getThreePerson(query) {
  return request({
    url: '/matrix/popup/threePerson',
    method: 'get',
    params: query
  })
}


//水闸弹窗-基础信息
export function getSluInfo(slcd) {
  return request({
    url: '/matrix/popup/getSluInfo?slcd=' + slcd,
    method: 'get',
  })
}

//水闸弹窗-雨水情
export function getHisTimeSluData(query) {
  return request({
    url: '/matrix/popup/getHisTimeSluData',
    method: 'get',
    params: query
  })
}

//水闸弹窗-仓库列表
export function getWareHouseBySlcd(slcd) {
  return request({
    url: '/matrix/popup/getWareHouse?slcd=' + slcd,
    method: 'get',
  })
}

//水闸弹窗-物资列表
export function getSluMaterial(query) {
  return request({
    url: '/matrix/popup/getSluMaterial',
    method: 'get',
    params: query
  })
}

//水质站弹窗
export function getWaterQulityStation(stcd) {
  return request({
    url: '/matrix/popup/getWaterQulityStation?stcd=' + stcd,
    method: 'get',
  })
}

//物资仓库弹窗-物资列表
export function getMaterialByWid(query) {
  return request({
    url: '/matrix/popup/getMaterial/list',
    method: 'get',
    params: query
  })
}

//物资仓库弹窗-仓库信息
export function getAssaultByWid(wid) {
  return request({
    url: '/matrix/popup/getAssault/' + wid,
    method: 'get',
  })
}

//雨量站弹窗
export function getRainfallDetail(stcd, timeType) {
  return request({
    url: '/matrix/popup/getRainfallDetail/' + stcd + '?timeType=' + timeType,
    method: 'get',
  })
}

//点播视频
export function videoPlay(deviceId, channelId) {
  return request({
    url: '/wvp/api/play/start/' + deviceId + '/' + channelId,
    method: 'get',
  })
}

//// 云台控制
export function videoControl(query) {
  return request({
    url: '/wvp/api/ptz/control/' + query.deviceId + '/' + query.channelId + '?deviceId=' + query.deviceId + '&channelId=' + query.channelId + '&command=' + query.command + '&horizonSpeed=' + query.horizonSpeed + '&verticalSpeed=' + query.verticalSpeed + '&zoomSpeed=' + query.zoomSpeed,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
  })
}
