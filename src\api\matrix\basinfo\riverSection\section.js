import request from '@/utils/request'

// 查询河流断面列表
export function listSection(query) {
  return request({
    url: '/matrix/section/list',
    method: 'get',
    params: query
  })
}

// 查询河流断面详细
export function getSection(did) {
  return request({
    url: '/matrix/section/' + did,
    method: 'get'
  })
}

// 新增河流断面
export function addSection(data) {
  return request({
    url: '/matrix/section',
    method: 'post',
    data: data
  })
}

// 修改河流断面
export function updateSection(data) {
  return request({
    url: '/matrix/section',
    method: 'put',
    data: data
  })
}

// 删除河流断面
export function delSection(did) {
  return request({
    url: '/matrix/section/' + did,
    method: 'delete'
  })
}

//保存断面关联测站关联关系
export function saveStation(data) {
  return request({
    url: "/matrix/section/saveRelation",
    method: "post",
    data: data
  })
}

//查询断面关联测站
export function getSaveStation() {}
