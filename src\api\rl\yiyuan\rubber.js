import request from '@/utils/request'

// 查询橡胶坝列表
export function listRubber(query) {
  return request({
    url: '/rl/rubber/list',
    method: 'get',
    params: query
  })
}

// 查询橡胶坝详细
export function getRubber(id) {
  return request({
    url: '/rl/rubber/' + id,
    method: 'get'
  })
}

// 新增橡胶坝
export function addRubber(data) {
  return request({
    url: '/rl/rubber',
    method: 'post',
    data: data
  })
}

// 修改橡胶坝
export function updateRubber(data) {
  return request({
    url: '/rl/rubber',
    method: 'put',
    data: data
  })
}

// 删除橡胶坝
export function delRubber(id) {
  return request({
    url: '/rl/rubber/' + id,
    method: 'delete'
  })
}
