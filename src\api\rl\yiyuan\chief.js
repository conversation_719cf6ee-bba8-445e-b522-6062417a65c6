import request from '@/utils/request'

// 查询河湖长信息管理列表
export function listChief(query) {
  return request({
    url: '/rl/water/chief/list',
    method: 'get',
    params: query
  })
}

// 查询河湖长信息管理详细
export function getChief(id) {
  return request({
    url: '/rl/water/chief/' + id,
    method: 'get'
  })
}

// 新增河湖长信息管理
export function addChief(data) {
  return request({
    url: '/rl/water/chief',
    method: 'post',
    data: data
  })
}

// 修改河湖长信息管理
export function updateChief(data) {
  return request({
    url: '/rl/water/chief',
    method: 'put',
    data: data
  })
}

// 删除河湖长信息管理
export function delChief(id) {
  return request({
    url: '/rl/water/chief/' + id,
    method: 'delete'
  })
}

// 查看河湖长信息管理
export function listHeduan(id) {
  return request({
    url: '/rl/water/chief/listHeduan/' + id,
    method: 'get'
  })
}

// 查看河湖长信息管理
export function listAllHeduan(id) {
  return request({
    url: '/rl/water/chief/listAllHeduan/' + id,
    method: 'get'
  })
}

// 维护河段信息
export function editHdByChiefId(data) {
  return request({
    url: '/rl/water/chief/editHdByChiefId/',
    method: 'post',
    data: data
  })
}
