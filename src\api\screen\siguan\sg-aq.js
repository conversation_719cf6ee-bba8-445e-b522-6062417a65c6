import request from '@/utils/request'

//四管-实时监测
export function monitor(query) {
  return request({
    url: '/matrix/baseline/siguan/monitor',
    method: 'get',
    params: query
  })
}

//四管-调度运用
export function disp_operation_type(query) {
  return request({
    url: '/matrix/baseline/siguan/disp_operation_type',
    method: 'get',
    params: query
  })
}
//四管-调度运用列表
export function disp_operation_list(query) {
  return request({
    url: '/matrix/baseline/siguan/disp_operation_list',
    method: 'get',
    params: query
  })
}

//四管-队伍
export function secure_team_type(query) {
  return request({
    url: '/matrix/baseline/siguan/secure_team_type',
    method: 'get',
    params: query
  })
}
//四管-应急预案列表
export function prepareplan_list(query) {
  return request({
    url: '/matrix/baseline/siguan/prepareplan_list',
    method: 'get',
    params: query
  })
}

//四管-值班信息
export function duty_info(query) {
  return request({
    url: '/matrix/baseline/siguan/duty_info',
    method: 'get',
    params: query
  })
}

//四管-洪水影响范围图
export function flood_image(query) {
  return request({
    url: '/matrix/baseline/siguan/flood_image',
    method: 'get',
    params: query
  })
}

//四管-物资
export function matrial_type(query) {
  return request({
    url: '/matrix/baseline/siguan/matrial_type',
    method: 'get',
    params: query
  })
}

//四管-专家
export function expert_type(query) {
  return request({
    url: '/matrix/baseline/siguan/expert_type',
    method: 'get',
    params: query
  })
}

//四管-培训列表
export function train_list(query) {
  return request({
    url: '/matrix/baseline/siguan/train_list',
    method: 'get',
    params: query
  })
}

//四管-实时水位/流量
export function water_level(query) {
  return request({
    url: '/matrix/baseline/siguan/water_level',
    method: 'get',
    params: query
  })
}

//四管-实时水位/流量 分页
export function water_level_page(query) {
  return request({
    url: '/matrix/baseline/siguan/water_level_page',
    method: 'get',
    params: query
  })
}

//四管-实时入库/出库流量
export function flow_level(query) {
  return request({
    url: '/matrix/baseline/siguan/flow_level',
    method: 'get',
    params: query
  })
}

//四管-实时入库/出库流量 分页
export function flow_level_page(query) {
  return request({
    url: '/matrix/baseline/siguan/flow_level_page',
    method: 'get',
    params: query
  })
}

//四管-抢险队伍
export function rescue_team(query) {
  return request({
    url: '/matrix/baseline/siguan/rescue_team',
    method: 'get',
    params: query
  })
}

//四管-抢险专家
export function experts(query) {
  return request({
    url: '/matrix/baseline/siguan/experts',
    method: 'get',
    params: query
  })
}

//四管-防汛物资
export function mt_manage(query) {
  return request({
    url: '/matrix/baseline/siguan/mt_manage',
    method: 'get',
    params: query
  })
}

//四管-培训管理名称列表
export function training_list(query) {
  return request({
    url: '/matrix/train/training_list',
    method: 'get',
    params: query
  })
}

//四管-培训管理
export function train(query) {
  return request({
    url: '/matrix/baseline/siguan/train',
    method: 'get',
    params: query
  })
}

//四管-值班管理
export function duty(query) {
  return request({
    url: '/matrix/baseline/siguan/duty',
    method: 'get',
    params: query
  })
}
//四管-值班管理
export function rain(query) {
  return request({
    url: '/matrix/baseline/siguan/rain',
    method: 'get',
    params: query
  })
}
//四管-值班管理
export function popup_prepareplan_list(query) {
  return request({
    url: '/matrix/baseline/siguan/popup_prepareplan_list',
    method: 'get',
    params: query
  })
}

//四管-水质监测
export function water_quality(query) {
  return request({
    url: '/matrix/baseline/siguan/water_quality',
    method: 'get',
    params: query
  })
}

//四管-安全-抢险队伍弹窗
export function pop_secure_team(query) {
  return request({
    url: '/matrix/baseline/siguan/pop_secure_team',
    method: 'get',
    params: query
  })
}

//四管-安全-抢险队伍弹窗
export function pop_warehouse(query) {
  return request({
    url: '/matrix/baseline/siguan/pop_warehouse',
    method: 'get',
    params: query
  })
}

//四管-安全-险情点位
export function pop_danger(query) {
  return request({
    url: '/matrix/baseline/siguan/pop_danger',
    method: 'get',
    params: query
  })
}
