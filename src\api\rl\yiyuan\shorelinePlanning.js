import request from '@/utils/request'

// 查询岸线规划填报列表
export function listPlanning(query) {
  return request({
    url: '/rl/shorelinePlanning/list',
    method: 'get',
    params: query
  })
}

// 查询岸线规划填报详细
export function getPlanning(id) {
  return request({
    url: '/rl/shorelinePlanning/' + id,
    method: 'get'
  })
}

// 新增岸线规划填报
export function addPlanning(data) {
  return request({
    url: '/rl/shorelinePlanning',
    method: 'post',
    data: data
  })
}

// 修改岸线规划填报
export function updatePlanning(data) {
  return request({
    url: '/rl/shorelinePlanning',
    method: 'put',
    data: data
  })
}

// 删除岸线规划填报
export function delPlanning(id) {
  return request({
    url: '/rl/shorelinePlanning/' + id,
    method: 'delete'
  })
}
