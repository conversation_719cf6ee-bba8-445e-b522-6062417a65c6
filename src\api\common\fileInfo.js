import request from '@/utils/request'
import { getToken } from "@/utils/auth";
import axios from "axios";

// 查询文件列表，传入外键关联id
export function getFileList(id) {
  return request({
    url: '/rl/common/file/' + id,
    method: 'get'
  })
}

// 根据文件主键查询文件列表信息
export function getFileListByIds(ids) {
  return request({
    url: '/rl/common/fileByIds',
    method: 'post',
    data: {
      ids: ids
    }
  })
}

/**
 * @param baseUrl process.env.VUE_APP_BASE_API
 * @param url 后台传过来的url，即(/2022/07/20/xxx.pdf)
 * @param fileName 指定文件名称及后缀，为空时，默认会解析url获取文件名，不需要指定时请传入空字符串或null
 */
export function downloadFile(baseUrl, url, fileName) {
  return axios({
    url: baseUrl + '/rl/common/download/new?name=' + url,
    method: 'get',
    headers: {
      Authorization: "Bearer " + getToken(),
    },
    responseType: 'blob'
  }).then(res => {
    let blob = new Blob([res.data], {
      type: 'application/octet-stream'
    })
    let link = document.createElement('a')
    let body = document.querySelector('body')
    link.href = window.URL.createObjectURL(blob)
    if (fileName !== null && fileName !== undefined && fileName !== '') {
      link.download = fileName
    } else {
      link.download = getFileName(url)
    }
    link.style.display = 'none'
    body.appendChild(link)
    link.click()
    body.removeChild(link)
    window.URL.revokeObjectURL(link.href)
  })
}

export function downloadFileBb(baseUrl, url, fileName) {
    return axios({
        url: baseUrl + '/rl/common/download/new1?name=' + url,
        method: 'get',
        headers: {
            Authorization: "Bearer " + getToken(),
        },
        responseType: 'blob'
    }).then(res => {
        let blob = new Blob([res.data], {
            type: 'application/octet-stream'
        })
        let link = document.createElement('a')
        let body = document.querySelector('body')
        link.href = window.URL.createObjectURL(blob)
        if (fileName !== null && fileName !== undefined && fileName !== '') {
            link.download = fileName
        } else {
            link.download = getFileName(url)
        }
        link.style.display = 'none'
        body.appendChild(link)
        link.click()
        body.removeChild(link)
        window.URL.revokeObjectURL(link.href)
    })
}

export function downloadImage(baseUrl, url) {
  let type = ''
  if (url.endsWith('jpg')) {
    type = 'image/jpeg'
  } else if (url.endsWith('png')) {
    type = 'image/png'
  }
  return axios({
    url: baseUrl + '/rl/common/download/image?name=' + url,
    method: 'get',
    headers: {
      Authorization: "Bearer " + getToken(),
    },
    responseType: 'blob'
  }).then(res => {
    let blob = new Blob([res.data], {
      type: type
    })
    return window.URL.createObjectURL(blob)
  })
}

export function downloadImageByFileId(baseUrl, id) {
    return axios({
        url: baseUrl + '/rl/common/download/imageById?id=' + id,
        method: 'get',
        headers: {
            Authorization: "Bearer " + getToken(),
        },
        responseType: 'blob'
    }).then(res => {
        let blob = new Blob([res.data])
        return window.URL.createObjectURL(blob)
    })
}

export function downloadMore(baseUrl, url,fileName) {
    let type = ''
    if (url.endsWith('jpg')) {
        type = 'image/jpeg'
    } else if (url.endsWith('png')) {
        type = 'image/png'
    }else if(url.endsWith('pdf')){
        type = 'application/pdf'
    }else {
        type= 'application/octet-stream'
    }
    return axios({
        url: baseUrl + '/rl/common/download/more?name=' + url,
        method: 'get',
        headers: {
            Authorization: "Bearer " + getToken(),
        },
        responseType: 'blob'
    }).then(res => {
        let blob = new Blob([res.data], {
            type: type
        })
        let link = document.createElement('a')
        let body = document.querySelector('body')
        link.href = window.URL.createObjectURL(blob)
        if (fileName !== null && fileName !== undefined && fileName !== '') {
            link.download = fileName
        } else {
            link.download = getFileName(url)
        }
        link.style.display = 'none'
        body.appendChild(link)
        link.click()
        body.removeChild(link)
        window.URL.revokeObjectURL(link.href)
    })
}

export function yulanMore(baseUrl, url) {
    let type = ''
    if (url.endsWith('jpg')) {
        type = 'image/jpeg'
    } else if (url.endsWith('png')) {
        type = 'image/png'
    } else if (url.endsWith('docx')) {
        type = 'image/jpeg'
    }else if (url.endsWith('doc')) {
        type = 'image/jpeg'
    }else if(url.endsWith('pdf')){
        type = 'image/jpeg'
    }
    return axios({
        url: baseUrl + '/rl/common/download/yulan?name=' + url,
        method: 'get',
        headers: {
            Authorization: "Bearer " + getToken(),
        },
        responseType: 'blob'
    }).then(res => {
        let blob = new Blob([res.data], {
            type: type
        })
        return window.URL.createObjectURL(blob)
    })
}

export function onlineShow(name){
    let type = ''
    if (name.endsWith('pdf')) {
        type = 'application/pdf;chartset=UTF-8'
    }
    return axios({
        url: process.env.VUE_APP_BASE_API+ '/rl/common/download/more?name=' + name,
        method: 'get',
        headers: {
            Authorization: "Bearer " + getToken(),
        },
        responseType: 'blob'
    }).then(res => {
        let blob = new Blob([res.data], {
            type: type
        });
        let href = window.URL.createObjectURL(blob); //创建下载的链接
        window.open(href)
    })
}

// 获取文件名称，name应传入url(/2022/07/20/xxx.pdf)
function getFileName(name) {
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1);
  } else {
    return "";
  }
}

export function getSysFileByBak(bak){
    return request({
        url: '/rl/common/selectSysFileByBak/'+bak,
        method: 'get'
    })
}
