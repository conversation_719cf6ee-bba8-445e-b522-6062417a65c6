import request from '@/utils/request'

// 查询堤防基本信息列表
export function listBank(query) {
  return request({
    url: '/matrix/bank/list',
    method: 'get',
    params: query
  })
}

// 查询堤防基本信息详细
export function getBank(bid) {
  return request({
    url: '/matrix/bank/' + bid,
    method: 'get'
  })
}

// 新增堤防基本信息
export function addBank(data) {
  return request({
    url: '/matrix/bank',
    method: 'post',
    data: data
  })
}

// 修改堤防基本信息
export function updateBank(data) {
  return request({
    url: '/matrix/bank',
    method: 'put',
    data: data
  })
}

// 删除堤防基本信息
export function delBank(bid) {
  return request({
    url: '/matrix/bank/' + bid,
    method: 'delete'
  })
}

//提防断面
//提防断面列表
export function getSectionList(query) {
  return request({
    url: '/matrix/matrix/list',
    method: 'get',
    params:query
  })
}

//查询详细信息
export function getSectionDetail(eid) {
  return request({
    url: '/matrix/matrix/' + eid,
    method: 'get'
  })
}

//新增
export function getSectionAdd(data) {
  return request({
    url: '/matrix/matrix',
    method: 'post',
    data
  })
}

//更新
export function getSectionUpdate(data) {
  return request({
    url: '/matrix/matrix',
    method: 'put',
    data
  })
}

//删除
export function getSectionDel(eid) {
  return request({
    url: '/matrix/matrix/' + eid,
    method: 'delete'
  })
}

