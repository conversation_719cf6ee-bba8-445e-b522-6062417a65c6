<template>
  <div class="ue4map" ref="Twin">
    <div id="player" style="display: none"></div>
    <i
      class="el-icon-setting"
      @click="
        () => {
          showFuncBox = !showFuncBox;
        }
      "
      style="position: fixed; top: 70px; right: 15px; color: #fff; z-index: 9999"
    ></i>
    <div class="funcBtnBox" v-show="showFuncBox">
      <el-collapse accordion>
        <el-collapse-item title="地图标点">
          <el-button @click="startGetCoord(undefined, undefined, 2)"
          >开启地图标点
          </el-button
          >
          <el-button @click="endGetCoord()">关闭地图标点</el-button>
        </el-collapse-item>
        <el-collapse-item title="外设监控">
          <el-button @click="startKeyboard()">注册键盘事件</el-button>
          <el-button @click="removeKeyboard()">删除键盘事件</el-button>
        </el-collapse-item>
        <el-collapse-item title="镜头动作">
          <el-button @click="personInspection()">人工巡查</el-button>
          <el-button @click="inspectionPause()">暂停巡查</el-button>
          <el-button @click="inspectionContinue()">继续巡查</el-button>
          <el-button @click="inspectionStop()">停止巡查</el-button>
          <el-button @click="initUAVView()">初始化无人机巡查视角</el-button>
          <!-- <el-button @click="UAVInspection()">无人机巡航</el-button> -->

          <el-button @click="sceneRoam()">开始镜头漫游</el-button>
          <!-- <el-button @click="startCameraRoam()">开始镜头漫游</el-button> -->
          <el-button @click="setCameraRoamState('pause')">暂停镜头漫游</el-button>
          <el-button @click="setCameraRoamState('continue')">继续镜头漫游</el-button>
          <el-button @click="setCameraRoamState('stop')">结束镜头漫游</el-button>
          <el-button @click="setCameraRotate(50, 'clockwise')"
          >镜头绕场景中心旋转开始
          </el-button
          >
          <el-button @click="setCameraRotate(50, 'stop')"
          >镜头绕场景中心旋转结束
          </el-button
          >
          <el-button @click="getCameraParams()">获取当前镜头信息</el-button>
          <el-button @click="resetCameraSpace('default')">边界限制</el-button>
          <el-button @click="resetCameraSpace('free')">开发边界限制</el-button>
        </el-collapse-item>
        <el-collapse-item title="场景渲染">
          <el-button @click="initMap()">场景渲染</el-button>
          <el-button @click="stopRenderCloud()">停止渲染</el-button>
          <el-button @click="setRenderQuality('low')">低场景渲染</el-button>
          <el-button @click="setRenderQuality('medium')">中场景渲染</el-button>
          <el-button @click="setRenderQuality('high')">高场景渲染</el-button>
          <el-button @click="setRenderQuality('epic')">超高场景渲染</el-button>
        </el-collapse-item>
        <el-collapse-item title="覆盖物">
          <el-button @click="addRoadHeat()">添加路径热力图</el-button>
          <el-button @click="updateRoadHeat()">更新路径热力图</el-button>
          <el-button @click="getFullSceenCoveringId()">获取屏幕内所有覆盖物ID</el-button>
          <el-button @click="updateCustomPOILabel()">更新label</el-button>
        </el-collapse-item>
        <el-collapse-item title="洪水演进">
          <el-button
            style="margin-left: 10px"
            @click="waterFloodFileLoadAndChangeGrid(name)"
          >1、网格下载及转置
          </el-button
          >
          <el-button @click="waterFloodDataUpload(1340, 20)"
          >2、淹没时序数据加载
          </el-button
          >
          <el-button @click="waterFloodPlay('floodsky')"
          >3、淹没过程播放水科院测试
          </el-button
          >
          <el-button @click="waterFloodPlay('flood40')">3、淹没过程播放40</el-button>
          <el-button @click="waterFloodPlay('flood200')">3、淹没过程播放200</el-button>
          <!-- <el-button @click=" waterFloodDataUpload('100', 'flood40')">接口100</el-button>
          <el-button @click=" waterFloodDataUpload('210724', 'flood210724')">接口210724</el-button> -->
          <el-button @click="waterFloodPlayOne('true', 281, 'flood40')"
          >40展示281帧
          </el-button
          >
          <el-input
            v-model="firstZhenNum"
            style="margin-left: 10px; width: 80%"
          ></el-input>
          <el-button @click="waterFloodPlayOne('true', firstZhenNum, 'flood40')"
          >40展示以上帧
          </el-button
          >
          <el-button @click="addWDRiver()"
          >添加三号支沟
          </el-button
          >
          <el-button @click="add3DText()"
          >添加3d文字
          </el-button
          >
          <el-input
            v-model="scendZhenNum"
            style="margin-left: 10px; width: 80%"
          ></el-input>
          <el-button @click="waterFloodPlayOne('true', scendZhenNum, 'floodsky')"
          >floodsky展示以上帧
          </el-button
          >
          <el-button @click="waterFloodDelateGrid(name)">删除转置网格</el-button>
          <!-- <el-button @click="addCustomPOI1(0)">添加label</el-button> -->
          <el-button @click="waterFlowSwitch(2)">水流开关</el-button>
          <el-button @click="waterFlowSwitch(3)">水流开关</el-button>
          <el-button @click="closeWaterFlowSwitch()">关闭水流开关</el-button>
          <el-button @click="gateSwitch()">闸门开关</el-button>
          <el-button @click="gateSwitchAndWaterFlowSwitch(true)"
          >闸门开启并开水花
          </el-button
          >
          <el-button @click="gateSwitchAndWaterFlowSwitch(false)"
          >闸门开启并关水花
          </el-button
          >
          <el-button @click="addWaterPolyline(0)">汛限水位线</el-button>
          <el-button @click="waterChange(38.5, 1)">抬升水面特定高度</el-button>
          <el-button @click="multiplePerspectives(0)">水库调度默认视角</el-button>
          <el-button @click="multiplePerspectives(1)">水库调度前方视角</el-button>
          <el-button @click="multiplePerspectives(2)">水库调度后方视角</el-button>
          <el-button @click="multiplePerspectives(3)">洪水演进默认视角</el-button>
          <el-button @click="multiplePerspectives(6)">库区默认视角</el-button>
          <el-button @click="deleteAllPoint()">清除所有点位</el-button>
        </el-collapse-item>
        <el-collapse-item title="水面">
          <el-button @click="getCoveringInfo()">获取实体属性</el-button>
          <el-button @click="waterGoup()">水面上升</el-button>
          <el-button @click="WaterDrow()">水面下降</el-button>
        </el-collapse-item>
        <el-collapse-item title="闸门">

        </el-collapse-item>
        <el-collapse-item title="测量工具">
          <el-button @click="measureTool()">测量工具</el-button>
          <el-button @click="measureAngle()">测量角度工具</el-button>
          <el-button @click="measureLength()">测量长度工具</el-button>
          <el-button @click="measureArea()">测量面积工具</el-button>
          <el-button @click="showHideCompass(true)">显示指南针</el-button>
          <el-button @click="showHideCompass(false)">隐藏指南针</el-button>
        </el-collapse-item>
        <el-collapse-item title="水面">
          <el-button @click="getAESWaterObjects()">获取水实体EID</el-button>
          <el-button @click="getCoveringWaterInfo()">获取实体属性</el-button>
          <el-input
            v-model="waterObject.height"
            disabled
            placeholder="当前水面高度"
          ></el-input>
          <el-input v-model="waterObject.maxHeight" placeholder="水面最大高度"></el-input>
          <el-input v-model="waterObject.minHeight" placeholder="水面最小高度"></el-input>
          <p @click="setLocalStorage" class="localClass">本地缓存</p>
          <el-slider
            v-if="waterObject.height != ''"
            @change="sliderValChange"
            width="100px"
            style="margin-left: 20px"
            v-model="sliderVal"
            :min="Number(waterObject.minHeight)"
            :max="Number(waterObject.maxHeight)"
          ></el-slider>
          <!-- <el-button @click="waterGoup()">水面上升</el-button> -->
        </el-collapse-item>
        <!-- <el-collapse-item title="场景编辑">
             <el-button @click="getAESObjects()">获取实体EID</el-button>
         </el-collapse-item> -->
        <el-collapse-item title="区域轮廓">
          <el-button @click="geoRange(1)">添加geo区域轮廓1</el-button>
          <el-button @click="geoRange(2)">添加geo区域轮廓2</el-button>
          <!-- <el-button @click="handleGeoJsonFile()">处理geojson文件</el-button> -->
          <el-button @click="showHidePOI('geo_range_id1', 'range', true)"
          >显示Geo区域轮廓1
          </el-button
          >
          <el-button @click="showHidePOI('geo_range_id1', 'range', false)"
          >隐藏Geo区域轮廓1
          </el-button
          >
          <el-button @click="showHidePOI('geo_range_id2', 'range', true)"
          >显示Geo区域轮廓2
          </el-button
          >
          <el-button @click="showHidePOI('geo_range_id2', 'range', false)"
          >隐藏Geo区域轮廓2
          </el-button
          >
        </el-collapse-item>
        <el-collapse-item title="POI点">
          <el-row v-for="item in digitalCon.videoPoints" :key="item.id">
            <el-col :span="24" style="padding-left: 10px">
              <el-button @click="showHidePOI(item.id, 'poi', true)"
              >显示{{ item.label }}
              </el-button
              >
            </el-col>
            <el-col :span="24" style="padding-left: 10px">
              <el-button @click="showHidePOI(item.id, 'poi', false)"
              >隐藏{{ item.label }}
              </el-button
              >
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item title="天气">
          <el-button @click="switchWeather('Sunny')">晴天</el-button>
          <!-- <el-button @click="switchWeather('Cloudy')">多云</el-button> -->
          <!-- <el-button @click="switchWeather('PartlyCloudy')">少云</el-button> -->
          <el-button @click="switchWeather('Overcast')">阴天</el-button>
          <el-button @click="switchWeather('LightRain')">小雨</el-button>
          <!-- <el-button @click="switchWeather('ModerateRain')">中雨</el-button> -->
          <!-- <el-button @click="switchWeather('HeavyRain')">大雨</el-button> -->
          <el-button @click="switchWeather('LightSnow')">小雪</el-button>
          <!-- <el-button @click="switchWeather('ModerateSnow')">中雪</el-button> -->
          <!-- <el-button @click="switchWeather('HeavySnow')">大雪</el-button> -->
          <!-- <el-button @click="switchWeather('Foggy')">雾天</el-button> -->
          <!-- <el-button @click="switchWeather('Sand')">扬尘</el-button> -->
          <!-- <el-button @click="switchWeather('Haze')">雾霾</el-button> -->
          <!-- <el-button @click="switchWeather('auto')">实时天气</el-button> -->

        </el-collapse-item>
        <el-collapse-item title="时间">
          <el-button
            v-for="v in 24"
            :key="v"
            @click="switchDate((v === 24 ? 0 : v) + ':00')"
          >
            {{ v + ":00" }}
          </el-button>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div v-if="showTree" style="position: absolute; right: 26%; top: -70px">
    </div>
    <div class="dt-title" v-if="showDtTitle">
      <div>数字孪生</div>
    </div>
    <div class="bj" v-if="loginAnimate">
      <div
        style="
          display: flex;
          flex-flow: column;
          align-items: center;
          justify-content: center;
          top: calc(50% - 135px);
          position: absolute;
          left: calc(50% - 130px);
          width: 260px;
        "
      >
        <div class="move">
          <img src="@/assets/images/digitalTwin/loading1.png" alt=""/>
        </div>
        <div style="color: #fff; font-size: 1rem; width: 100%">
          <div class="loadingText">加载中...</div>
          <div class="loading">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cloudRenderer from "51superapi";
import Bus from "@/views/screen/bus.js";
import {digitalTwinApi, personModelInfo} from "@/utils/mapUtils/digitalTwinUtils";
import axios from "axios";
import * as constants from "constants";
import {getToken} from "@/utils/auth";
import {
  addHydrological,
  addGJHydrological,
  addSSGJHydrological,
  getsecureTeam,
  getwareHouse,
  addHydrological2,
} from "./mapPoint";

// import commonDialogBox from "@/components/commonDialogBox/commonDialogBox.vue";

// import { getRenderList} from "@/api/map/dt/common";
export default {
  name: "",
  components: {},
  // mixins: [screenCommon],
  data() {
    return {
      RainfallData:'',
      SHZGSYpoiList: [],
      scendZhenNum: 0,
      firstZhenNum: 0,
      controlManage: false,
      treeData: [],
      treeExpandedKeys: [],
      treeCheckedKeys: [],
      //视频弹窗
      videoOpen: false,
      //设备id
      deviceId: "",
      //通道id
      id: "",
      cloudRenderTimer: null, // 场景重新渲染定时器
      cloudRerenderSum: 0, // 重新渲染总次数 超过次数后不再重新渲染
      cloudRerenderCount: 0, // 单次重新渲染时请求次数 默认每次重连可请求三次
      cloudRender: null, //渲染对象
      shadeIss: true, //遮罩显示
      loginAnimate: true,
      percentage: 0,
      customColor: "#409eff",
      timer: "",
      showFuncBox: false,
      unInitPoiList: [],
      poiList: [],
      spdList: [],
      dmList: [],
      controlData: true,
      // 和风天气map
      weatherMap: {
        晴: "Sunny",
        晴天: "Sunny",
        多云: "Cloudy",
        少云: "PartlyCloudy",
        阴天: "Overcast",
        阴: "Overcast",
        小雨: "LightRain",
        中雨: "ModerateRain",
        大雨: "HeavyRain",
        雨夹雪: "LightSnow",
        小雪: "LightSnow",
        中雪: "ModerateSnow",
        大雪: "HeavySnow",
        雾天: "Foggy",
        雾: "Foggy",
        扬尘: "Sand",
        雾霾: "Haze",
      },
      // 和风天气城市名称
      weatherCityName: null,
      // 和风天气城市编码
      weatherCityCode: null,
      // 和风天气城市列表
      weatherCityList: [],
      // 镜头漫游点位数组
      cameraRoamPoints: [],
      zhamenheight: 8, //闸门初始上升高度
      zhamen_1_UpId: null,
      zhamen_1_DownId: null,
      zhamen_2_UpId: null,
      zhamen_2_DownId: null,
      zhamen_3_UpId: null,
      zhamen_3_DownId: null,
      zhamen_1_State: true,
      zhamen_2_State: true,
      zhamen_3_State: true,
      waterObject: {
        eid: "",
        lonlat: "",
        height: "",
        maxHeight: "",
        minHeight: "",
      },
      sliderVal: 0,
      oldSliderVal: 0,
      //闸门控制,
      value1: false,
      value2: false,
      value3: false,
      //管道控制
      value: [],
      poiId: [],
      optionsPipe: [
        {
          value: "pipeOperator",
          label: "管道操作",
          children: [
            {
              value: "pipeUp",
              label: "管道抬升",
            },
            {
              value: "pipeDown",
              label: "管道恢复",
            },
          ],
        },
        {
          value: "oldPipe",
          label: "旧管道",
          children: [
            {
              value: "oCsgs",
              label: "城市供水",
            },
            {
              value: "oNtgg",
              label: "农田灌溉",
            },
            {
              value: "oYCgg",
              label: "渔场供水",
            },
          ],
        },
        {
          value: "newPipe",
          label: "新管道",
          children: [
            {
              value: "nCsgs",
              label: "城市供水",
            },
            {
              value: "nNtgg",
              label: "农田灌溉",
            },
          ],
        },
        {
          value: "nsbd",
          label: "南水北调",
          children: [
            {
              value: "ns-gs",
              label: "双向供水",
            },
            {
              value: "ns-dgs",
              label: "单向供水",
            },
            {
              value: "xs-fs",
              label: "蓄放水",
            },
          ],
        },
        {
          value: "dm",
          label: "断面操作",
          children: [
            {
              value: "dmts",
              label: "断面提升",
            },
            {
              value: "dmhf",
              label: "断面恢复",
            },
          ],
        },
      ],
      options: {
        monitorsPoints: null,
      },
      visible: false,
      //控制放水洞显示
      state: false,
      holeShowFade: "放水洞隐藏",
      digitalCon: {},
      cloudUrls: [
        {label: "云服务", value: "https://vizservice-paas.51aes.com"},
        {label: "公司服务", value: "https://twin.zhywater.com:28889"},
        {label: "横山水库内网服务器", value: "http://*************:8889"},
        {label: "油车水库内网服务器", value: "http://************:8889"},
        {label: "区域专线服务器", value: "http://*************:8889"},
        {label: "区域专线连横山用", value: "http://************:8889"},
        {label: "区域专线连油车用", value: "http://************:8889"},
        {label: "本机地址https", value: "https://localhost:8889"},
        {label: "本机地址http", value: "http://localhost:8889"},
      ],
      reconnectInfo: {
        open: false,
        title: null,
        label: null,
        url: null,
      },
      personModelInfo: {
        eid: "-9150751364756233164",
        path: [],
        speed: 3,
        times: 1,
        cameraTrace: false,
        state: 0,
        distance: 10,
      },
      infoLog: {
        diaLogName: "infoLog",
        resultshow: false,
        dialogFull: false,
        title: "",
        width: "50%",
        data: {},
        type: "1",
        id: "vrvideo",
      },
      // 演进模型参数---已完成后端配置化
      floodParams: [
        {
          isPreLoad: true,
          remark: "",
          projectId: 6,
          projectIndex: "BX",
          preLoadItems: [
            {
              floodId: "f1000",
              loaded: false,
              isEnable: true,
              dataUrl: null,
              isLocalfloodBasicData: true,
              floodBasicData: {
                tifURL: "D:\\51TIFData\\flood-shzg\\shzg",
                shpURL: "D:\\51TIFData\\flood-shzg\\shzg",
                shxURL: "D:\\51TIFData\\flood-shzg\\shzg",
                prjURL: "D:\\51TIFData\\flood-shzg\\shzg",
                dbfURL: "D:\\51TIFData\\flood-shzg\\shzg",
              },
            },
            // {
            //     "floodId": "whssklimitwl",
            //     "loaded": false,
            //     "isEnable": true,
            //     "dataUrl": null,
            //     "isLocalfloodBasicData": true,
            //     "floodBasicData": {
            //         "tifURL": "D:\\51TIFData\\flood-whssk\\wohushan84",
            //         "shpURL": "D:\\51TIFData\\flood-whssk\\wohushan84",
            //         "shxURL": "D:\\51TIFData\\flood-whssk\\wohushan84",
            //         "prjURL": "D:\\51TIFData\\flood-whssk\\wohushan84",
            //         "dbfURL": "D:\\51TIFData\\flood-whssk\\wohushan84"
            //     }
            // }
          ],
        },
      ],
      socket: null, //WebSocket
      totalPage: "",
      tmidxList: [], //
      sockeData: [],
      plcd: null,
      viewName: '',
      handleValue: null,
      //洪水演进  数据接口·
      queryForm: {
        schemeCode: "",
        schemeName: "",
        inputs: [
          {
            tm: "2024-06-12 06:00:00",
            q: 1000.0,
            z: 0.0,
          },
          {
            tm: "",
            q: 1000.0,
            z: 0,
          },
        ],
        dataSourceType: 1,
        forecastType: 1,
      },
    };
  },
  props: {
    digitalProps: {
      type: Object,
      default: function () {
        return {};
      },
    },
    mapCon: {
      type: Object,
      default: function () {
        return {};
      },
    },
    showTree: {
      type: Boolean,
      default: true,
    },
    showDtTitle: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    mapCon(value) {
      if (value && value.treeCon && value.treeCon.treeData) {
        let list = [];
        value.treeCon.treeData.forEach((item) => {
          if (item.id == "ssjk") {
            let children = item.children;
            if (children) {
              // 去掉 实时监控中的 水库水文站、水质站、流量站 选项
              // 添加 水位站
              children = children.filter(
                (ci) => ci.id != "ssjk-skz" && ci.id != "ssjk-szz"
              );
              children.push({
                tParentId: 36,
                showIndex: false,
                showPicture: false,
                id: "ssjk-swz",
                label: "水位站",
                type: "2",
                tId: 999,
              });
              item.children = children;
            }
            list.push(item);
          }
          if (item.id == "fybz") {
            list.push(item);
          }
        });
        this.treeData = list;
      }
    },
    "$store.getters.select51": {
      handler(newVal) {
        let activeSelect = JSON.parse(newVal);
        if (activeSelect.obj.id == "ssjk") {
          if (activeSelect.obj.children.length != 0) {
            for (let x = 0; x < activeSelect.obj.children.length; x++) {
              if (activeSelect.obj.children[x].id == "ssjk-spd") {
                for (let i = 0; i < this.spdList.length; i++) {
                  let jsondata = {
                    id: this.spdList[i], //覆盖物id
                    covering_type: "poi", //覆盖物类型, 详见下表
                    bshow: activeSelect.flag, //true:显示; false:隐藏
                  };
                  digitalTwinApi.ShowHideCovering(jsondata);
                }
              } else if (activeSelect.obj.children[x].id == "ssjk-dmd") {
                for (let i = 0; i < this.dmList.length; i++) {
                  let jsondata = {
                    id: this.dmList[i], //覆盖物id
                    covering_type: "poi", //覆盖物类型, 详见下表
                    bshow: activeSelect.flag, //true:显示; false:隐藏
                  };
                  digitalTwinApi.ShowHideCovering(jsondata);
                }
              }
            }
          }
        }
        if (activeSelect.obj.id == "ssjk-spd") {
          for (let i = 0; i < this.spdList.length; i++) {
            let jsondata = {
              id: this.spdList[i], //覆盖物id
              covering_type: "poi", //覆盖物类型, 详见下表
              bshow: activeSelect.flag, //true:显示; false:隐藏
            };
            digitalTwinApi.ShowHideCovering(jsondata);
          }
        } else if (activeSelect.obj.id == "ssjk-dmd") {
          for (let i = 0; i < this.dmList.length; i++) {
            let jsondata = {
              id: this.dmList[i], //覆盖物id
              covering_type: "poi", //覆盖物类型, 详见下表
              bshow: activeSelect.flag, //true:显示; false:隐藏
            };
            digitalTwinApi.ShowHideCovering(jsondata);
          }
        }
      },
      deep: true,
      immediate: false,
    },

  },
  created() {
    this.digitalCon = this.digitalProps;
    let waterObj = localStorage.waterObj;
    if (waterObj != "" && waterObj != null && waterObj != undefined) {
      this.waterObject = JSON.parse(waterObj);
      this.sliderVal = this.waterObject.height;
      this.oldSliderVal = this.waterObject.height;
    }
    //更新渲染地址列表
    // getRenderList().then((res) => {
    //   if(res.code==200){
    //   this.cloudUrls=res.rows;
    //   }
    // });
  },
  methods: {
    dialogClose(data) {
      this.infoLog.resultshow = false;
    },
    //工具集--重新设置渲染窗口大小
    resetVideoSize() {
      if (this.cloudRender) {
        this.cloudRender.SuperAPI(
          "SetStylePlayer",
          "position:absolute;width:100%;height:100%;top:50%;left:0%;transform:translate(0,-50%);"
        );
        this.cloudRender.SuperAPI("ResetVideo");
      }
    },
    //配置化--弹窗重连渲染
    reconnectRender() {
      // this.cloudRender.SuperAPI("StopRenderCloud")
      // 启动云渲染
      this.cloudRender = new cloudRenderer("player", 0);
      window.cloudRender = this.cloudRender;
      if (this.cloudRenderTimer != null) {
        this.cloudRerenderCount = this.cloudRerenderCount + 1;
      }
      this.GetUrlStartRenderCloud(this.reconnectInfo.url, this.digitalCon.orderID);
      this.reconnectInfo.open = false;
    },
    //工具集--设置水面对象缓存
    setLocalStorage() {
      localStorage.waterObj = JSON.stringify(this.waterObject);
    },
    //工具集--开启获取aes实体对象
    getAESWaterObjects() {
      //获取水面的Eid
      let that = this;
      let jsondata = {
        state: true, //true:开启获取EID; false:关闭获取EID
        highlight: true, //true:点击实体高亮; false:点击实体不高亮
        //"act_layers":[] //可被选中元素所在图层，删除该字段，全部元素可被选中
      };
      that.cloudRender.SuperAPI("StartGetEID", jsondata).then((_back) => {
        let data = JSON.parse(_back);
        that.waterObject.eid = data.args.eid;
      });
    },
    //工具集--获取eid对应实体的基本信息
    getCoveringWaterInfo(arr) {
      //根据eid获取实体类的属性
      let that = this;
      if (that.waterObject.eid == "") {
        return this.$message.error("请先确定水面EID");
      }
      let jsondata = {
        eid: [that.waterObject.eid],
      };
      window.cloudRender.SuperAPI("GetAESObjectDataWithEids", jsondata, (_back) => {
        let data = JSON.parse(_back);
        this.waterObject.height = Number(Number(data.args.data[0].altitude).toFixed(0));
        this.waterObject.maxHeight = this.waterObject.height + 10;
        this.waterObject.minHeight = this.waterObject.height - 10;
        this.waterObject.lonlat =
          data.args.data[0].coord.longitude + "," + data.args.data[0].coord.latitude;
        this.sliderVal = this.waterObject.height;
        this.oldSliderVal = this.waterObject.height;
        return data;
      });
    },
    //工具集--根据上面获取的waterObject改变水面高度
    sliderValChange() {
      let that = this;
      let waterId = "waterId" + Math.floor(Math.random() * (1 - 1000) + 1000);

      digitalTwinApi.addWaterPathSet(
        waterId,
        this.waterObject.lonlat,
        this.oldSliderVal,
        this.sliderVal
      );
      setTimeout(() => {
        this.oldSliderVal = this.sliderVal;
        that.waterMoveTo(that.waterObject.eid, waterId);
      }, 100);
    },
    //配置化--进度条进行更新，用于假的进度标识
    // changePercentage() {
    //   if (this.percentage == 50) {
    //     this.percentage;
    //     return;
    //   }
    //   this.percentage++;
    // },
    // 配置化--初始化渲染51场景
    initMap() {
      //let cloudurl = '/51worldServer', // 'http://************:8890', //云渲染服务地址 (Cloud rendering service address) ①本地IP: http://***********:8889, ②域名: https://vizservice.51hitech.com
      // this.digitalCon.cloudUrl="http://*************:8889";
      //  orderID = '36a47E82'; //渲染口令, 在云渲染客户端上获得 (Project ID, obtained on the cloud rendering client)
      // console.log(this.digitalCon,"this.digitalCon");

      if (this.digitalCon.cloudUrl && this.digitalCon.orderID) {
        // 启动云渲染
        this.cloudRender = new cloudRenderer("player", 0);
        window.cloudRender = this.cloudRender;
        if (this.cloudRenderTimer != null) {
          this.cloudRerenderCount = this.cloudRerenderCount + 1;
        }
        this.reconnectInfo.url = this.digitalCon.cloudUrl;
        this.GetUrlStartRenderCloud(this.digitalCon.cloudUrl, this.digitalCon.orderID);
      } else {
        this.$modal.msgWarning("渲染信息不能为空！");
      }
    },
    //配置化--链接渲染
    GetUrlStartRenderCloud(cloudurl, orderID) {
      let that = this;
      fetch(`${cloudurl}/Renderers/Any/order`, {
        method: "POST",
        headers: {"Content-type": "application/json"},
        body: JSON.stringify({
          order: orderID,
          width: window.innerWidth,
          height: window.innerHeight,
        }),
        //① order 渲染口令必填; ② width, height: 设置云渲染输出分辨率(此设置为固定分辨率,可选; 默认以云渲染客户端设置的分辨率输出)
      })
        .then((res) => {
          if (!res.ok) {
            throw Error(res.statusText);
            // that.refreshCloudRender(true)
          }
          return res.json();
        })
        .then((json) => {
          if (json.errCode != null && json.errCode !== undefined) {
            // if (json.message != null && json.message !== undefined) {
            //   this.$modal.msgWarning(json.message)
            //   return
            // } else {
            //   let text = constants[json.errCode]
            //   if (text) {
            //     this.$modal.msgWarning(text)
            //     return
            //   }
            // }
            that.reconnectInfo.title = "重新连接";
            that.reconnectInfo.label = json.message
              ? json.message
              : constants[json.errCode];
            that.reconnectInfo.open = true;
            return;
          }
          if (json.url) {
            that.refreshCloudRender(false);
            //启动云渲染
            //that.cloudRender.SuperAPI("StartRenderCloud", json.url);
            // 开启WASD方向键事件
            that.cloudRender.SuperAPI("StartRenderCloud", json.url, "keyboard");
            //事件注册
            that.cloudRender.SuperAPI(
              "RegisterCloudResponse",
              that.myHandleResponseFunction
            );

            that.cloudRender.SuperAPI_onRenderCloudError = function () {
              console.log("云渲染异常");
            };

            that.cloudRender.SuperAPI_onUnavailableRender = function () {
              console.log("未获取到渲染资源.");
            };

            that.cloudRender.SuperAPI_onStopedRenderCloud = function () {
              console.log("云渲染关闭或通信息中断");
              that.loginAnimate = true;
              that.loginAnimate = true;
              that.reconnectInfo.title = "重新连接";
              that.reconnectInfo.title = "云渲染关闭或通信息中断";
              that.reconnectInfo.open = true;
              // that.refreshCloudRender(true)
            };

            that.cloudRender.SuperAPI_onRenderCloudConnected = function () {
              console.log("云服务连接成功");
            };

            // setTimeout(() => {
            //   // 设置视角
            //   that.setDefaultView();
            // }, 10000);
          }
        })
        .catch((error) => {
          console.error("51 Error: ", error);
          // that.refreshCloudRender(true)
        });
    },
    //配置化--刷新渲染
    refreshCloudRender(state) {
      let that = this;
      if (state) {
        console.log("重新渲染总次数：" + this.cloudRerenderSum);
        console.log("重新渲染次数：" + this.cloudRerenderCount);
        if (this.cloudRerenderSum > 3) {
          clearTimeout(this.cloudRenderTimer);
          this.cloudRenderTimer = null;
          console.log(
            "重新渲染总次数超过3次后将不再重新渲染，如需渲染页面请重新刷新页面"
          );
        } else if (this.cloudRerenderCount > 3) {
          console.log("51场景重新渲染失败");
          this.$modal.msgWarning("51场景渲染失败! 请检查网络或刷新页面。");
          clearTimeout(this.cloudRenderTimer);
          this.cloudRenderTimer = null;
          this.cloudRerenderCount = 0;
        } else {
          this.cloudRerenderSum = this.cloudRerenderSum + 1;
          console.log("51场景重新渲染：" + this.cloudRerenderCount);
          clearTimeout(this.cloudRenderTimer);
          this.cloudRenderTimer = null;
          this.cloudRenderTimer = setTimeout(() => {
            that.initMap();
          }, 5000);
        }
      } else {
        console.log("清除重新渲染场景定时任务");
        clearTimeout(this.cloudRenderTimer);
        this.cloudRenderTimer = null;
        this.cloudRerenderCount = 0;
      }
    },
    //配置化--设置场景视角
    setDefaultView(jsondata) {
      if (jsondata != null) {
        this.cloudRender.SuperAPI("SetCameraInfo", jsondata).then((_back) => {
        });
      }
    },
    //工具集--51场景的各种回调事件
    myHandleResponseFunction(data) {
      let that = this;
      let jsonObject = JSON.parse(data);
      switch (jsonObject.func_name) {
        case "APIAlready":
          // 3D世界加载完成 do Something
          let videoDom = document.getElementById("streamingVideo");
          videoDom.style.objectFit = "fill";
          videoDom.style.height = "100%";
          this.cloudRender.SuperAPI(
            "SetStylePlayer",
            "position:absolute;width:100%;height:100%;top:50%;left:0%;transform:translate(0,-50%);"
          );
          this.cloudRender.SuperAPI("ResetVideo");
          this.cloudRender.SuperAPI("SetResolution", {
            width: window.innerWidth,
            height: window.innerHeight,
          });
          window.addEventListener("resize", () => {
            this.cloudRender.SuperAPI(
              "SetStylePlayer",
              "position:absolute;width:100%;height:100%;top:50%;left:0%;transform:translate(0,-50%);"
            );
            this.cloudRender.SuperAPI("ResetVideo");
            this.cloudRender.SuperAPI("SetResolution", {
              width: window.innerWidth,
              height: window.innerHeight,
            });
          });
          this.cloudRender.SuperAPI("keyboard");
          let MapViewerOption = that.digitalCon.firstMapViewerOption;
          // personModelInfo.mapViewerOptions = this.digitalCon.mapViewerOptions;
          let windowHref = window.location.href;
          that.setDefaultView(MapViewerOption)
          that.addWDRiver()//河道底部添加矢量数据
          setTimeout(() => {
            that.loginAnimate = false;
          }, 1000);
          //添加汛限水位线
          // this.addWaterPolyline(0)
          if (that.$route.path === "/screen/siyu/yubao") {
            that.getHydrologicalData()
          } else if (that.$route.path === "/screen/siyu/yujing") {
            that.getGJTideLevelData()
          }else if (that.$route.path === "/screen/siyu/yuan") {
            that.getWareHousePoint()
            that.getSecureTeamPoint()
          }
          //添加水文站
          // that.getHydrologicalData()
          //添加告警水文站
          // that.getGJTideLevelData()
          //添加实时告警水文站
          // that.getSSGJTideLevelData()
          // that.getWareHousePoint()
          // that.getSecureTeamPoint()
          if(that.digitalCon.waterElevation){
            that.setWaterFaceElevation(that.digitalCon.waterElevation);
          }
          that.setCameraSpace();
          setTimeout(() => {
            // if (
            //   that.digitalCon.projectIndex == "BX" &&
            //   that.digitalCon.floodParams
            // ) {
            //   this.floodParams = that.digitalCon.floodParams;
            console.log("后端预加载参数", this.floodParams);
            for (let i = 0; i < this.floodParams.length; i++) {
              if (this.floodParams[i].isPreLoad) {
                this.waterFloodFileLoadAndChangeGrid(
                  this.floodParams[i].preLoadItems[0].floodId,
                  this.floodParams[i].preLoadItems[0]
                );
                this.floodParams[i].preLoadItems[0].loaded = true;
              }
            }
            // }
          }, 10000);
          break;
        case "beginPlay":
          setTimeout(function () {
            that.switchWeather("Sunny");
            that.switchDate("12:00");
            let labelTimer = null;
            //初始化完成后回调
            that.$emit("afterInitDigitalTwin");
            Bus.$on("boardCoord", (e) => {
              if (e) {
                try {
                  const {latitudeWgs84, longitudeWgs84} = e;
                  e.coord = Number(longitudeWgs84) + "," + Number(latitudeWgs84);
                  e.coord_z = 33.720001220703125;
                  e.altitude = 33.720001220703125;
                  e.time = new Date().getTime();
                  //作位置偏移调整
                  e.coord =
                    Number(e.longitudeWgs84 - 0.000019) +
                    "," +
                    Number(e.latitudeWgs84 - 0.000051);
                  //为了避免重新更新标签，加上时间限制
                  if ((e.time - labelTimer) / 1000 > 4) {
                    digitalTwinApi.addWaterCoord(e);
                    labelTimer = new Date().getTime();
                  }
                  digitalTwinApi.boardSocketCopy(e, e.deviceId);
                  // digitalTwinApi.boardSocketCopy2(e, e.deviceId)
                  // digitalTwinApi.boardSocketCopy3(e, e.deviceId)
                  // digitalTwinApi.boardSocketCopy4(e, e.deviceId)
                } catch (error) {
                  // throw new Error(error)
                  console.log(error, "error");
                }
              }
            });
            let jsondata = {
              state: true, //true:开启获取EID; false:关闭获取EID
              highlight: false, //true:点击实体高亮; false:点击实体不高亮
            };
            window.cloudRender.SuperAPI("StartGetEID", jsondata).then((_back) => {
              console.log(_back);
            });
          }, 10000);

          break;
        case "OnSuperAPIInputActionStart":
          break;
        case "OnAddCustomPOISuccess":
          //console.log('OnAddCustomPOISuccess', jsonObject)
          break;
        case "OnPathClick":
          //console.log('OnPathClick', '覆盖物点击事件', jsonObject)
          break;
        case "OnStartGetEIDResult":
          if (jsonObject.args.eid == "-9150751357353827118") {
            window.open(
              "http://192.168.1.30:7100/jumpPage?userName=yixing&password=VPscNcQcMdrr6ZQs+tgc7j4SEkerT2OymFkr5GWdU1msxUbQGEBv+hSCWLjmq2ZGvV1Tz1F2EUt5rjBqdP0BboezR6VpSsJ1EEmziHv1GV5F2ddSX6tQtttvRqkrAxAUuk2hRWV/Z6w9Gj8VCDvSjGu0oVlp47fhLB5PKC8Hvww=",
              "_blank"
            );
          } else if (jsonObject.args.eid == "-9150751360376084702") {
            digitalTwinApi.waterLabelShow = true;
            window.cloudRender
              .SuperAPI("ShowHideCovering", {
                id: "09db4280e29045b9a375b14a94cb2c04", //覆盖物id
                covering_type: "poi", //覆盖物类型, 详见下表
                bshow: digitalTwinApi.waterLabelShow, //true:显示; false:隐藏
              })
              .then((_back) => {
                console.log(_back);
              });
          }
          break;
        case "OnPOIClick":
          //点击事件；
          console.log("OnPOIClick", "POI点击事件", jsonObject);

          if (jsonObject.args && jsonObject.args.id) {
            // console.log(this.poiList);
            let point = this.SHZGSYpoiList.find((item) => {
              return (
                item.id == jsonObject.args.id || item.deviceCode === jsonObject.args.id
              );
            });
            if (point) {
              console.log("点击点位信息", point, point.point_type);
              var digitalData = {};
              switch (point.point_type) {
                case "ssjk-swz":
                  digitalData.name = "ssjk-swz";
                  break;
                case "ssjk-swzsj":
                  digitalData.name = "ssjk-swzsj";
                  break;
                case "ssjk-swzsc":
                  digitalData.name = "ssjk-swzsc";
                  break;
                  case "ssjk-yqz":
                  digitalData.name = "ssjk-yqz";
                  break;
                default:
                  break;
              }
              digitalData.data = point;
              if (digitalData.name != null) {
                this.$emit("clickPoint", {digitalData})
              }
              // 向上返回点击回调信息
              this.$emit("OnPOIClick", point);
              // 隐藏POI的label
              let jo = {
                id: point.id,
                always_show_label: false,
                show_label_range: point.show_label_range,
                sort_order: false,
                state: point.state,
                animation_type: point.animation_type,
                duration_time: 0,
              };
              digitalTwinApi.UpdateCustomPOIStyle(jo);
            }
          }
          break;
        //模拟指定类型的覆盖物点击
        case "OnSimClickCoveringSuccess":
          let jsondata = {
            id: "aes_object", //覆盖物id
            selected_state: true, //覆盖物是否被选中; true:选中; false:未选中
            covering_type: "path", //覆盖物类型, 详见下表
          };
          window.cloudRender.SuperAPI("SimClickCovering", jsondata).then((_back) => {
            console.log(_back);
          });
          break;
        case "OnPOIHover":
          //POI点击事件；
          break;
        case "OnPOIUnHover":
          //POI点击事件；
          break;
        case "OnCustomWebJsEvent": //window与3D世界通信
          //点击事件；
          //this.$emit('OnCustomWebJsEvent', jsonObject);
          break;
        case "OnOpenMapSuccess":
          //this.$emit('OnOpenMapSuccess');
          break;
        case "OnPOILabelClick": //鼠标点击自定义POI label 回调通知
          console.log("OnPOILabelClick", "鼠标点击自定义POI label 回调通知", jsonObject);

          if (jsonObject.args && jsonObject.args.id) {
            let point = this.poiList.find((item) => {
              return item.id == jsonObject.args.id;
            });
            if (point) {
              this.$emit("OnPOIClick", point);
            }
            // 告警点
            let alarmPoint = personModelInfo.poiList.find((item) => {
              return item.id == jsonObject.args.id;
            });
            if (alarmPoint) {
              this.$emit("OnPOIClick", alarmPoint);
            }
          }
          break;
        case "OnFocusEffectEnd": //Focus动作开始, 结束 回调通知
          //this.$emit('OnFocusEffectEnd');
          break;
        case "OnFocusAllEffectEnd": //Focus动作开始, 结束 回调通知
          //this.$emit('OnFocusAllEffectEnd');
          break;
        case "OnFocusPOIEnd": //聚焦完毕回调；
          //this.$emit('OnFocusPOIEnd');
          break;
        case "OnAnimationFinished": //动画放完；
          //this.$emit('OnAnimationFinished');
          break;
        case "OnCloseMapSuccess": //关闭地图回调
          //this.$emit('OnCloseMapSuccess');
          break;
        case "OnHomeButtonClicked": //返回按钮   点击池州九华
          //this.$emit('OnHomeButtonClicked');
          break;
        case "OnCameraRoamingProStart": //漫游开始
          console.log("1111111");
          //this.$emit('OnCameraRoamingProStart');
          break;
        case "OnCameraRoamingProEnd": //漫游结束
          // this.cameraMoveState();
          //this.$emit('OnCameraRoamingProEnd');
          break;
        case "OnCoverToMoveSuccess": // 覆盖物移动
          //this.$emit('OnCoverToMoveSuccess', jsonObject);
          // 判断是否是人工巡查 是的话 定时查询当前人物模型位置信息
          if (personModelInfo.state != 0) {
            /*let videoPointList = this.poiList.filter(
              (item) =>
                item.point_type == "ssjk-spd" &&
                // && item.params.label != '闸阀室内'
                item.params.label != "溢洪闸看2号门"
            );*/
            let sywyPointList = this.poiList.filter(
              (item) => item.point_type == "ssjk-wy" || item.point_type == "ssjk-sy"
            );
            if (personModelInfo.timer == null) {
              personModelInfo.timer = setInterval(() => {
                let jo = {
                  eid: [personModelInfo.eid],
                };
                this.cloudRender.SuperAPI("GetAESObjectDataWithEids", jo, (_back) => {
                  let data = JSON.parse(_back);
                  let coordInfo = data.args.data[0].bounding_center_coord;
                  /*if (videoPointList && videoPointList.length > 0 && coordInfo) {
                  let min = 0;
                  let vd = null;
                  videoPointList.forEach((item, index) => {
                    let lnglat = item.coord.split(",");
                    let distance = this.GetDistance(
                      Number(lnglat[1]),
                      Number(lnglat[0]),
                      Number(coordInfo.y),
                      Number(coordInfo.x)
                    );
                    //console.log('distance',item.label.content[0].text[0], distance)
                    if (index === 0) {
                      min = distance;
                      vd = item;
                    } else {
                      if (min > distance) {
                        min = distance;
                        vd = item;
                      }
                    }
                  });
                  this.$store.commit("map/SET_PERSON_PATROL_VIDEO_POINT_INFO", vd);
                }*/
                  if (sywyPointList && sywyPointList.length > 0 && coordInfo) {
                    let list = [];
                    sywyPointList.forEach((item, index) => {
                      let lnglat = item.coord.split(",");
                      let distance = this.GetDistance(
                        Number(lnglat[1]),
                        Number(lnglat[0]),
                        Number(coordInfo.y),
                        Number(coordInfo.x)
                      );
                      if (distance <= 50) {
                        list.push(item);
                      }
                    });
                    this.$store.commit("map/SET_PERSON_PATROL_WYSY_POINT_LIST", list);
                  }
                });
              }, 2000);
            }
          }
          break;
        case "OnUpdateEffectCoordSuccess": // 场景特效更新坐标
          //this.$emit('OnCoverToMoveSuccess', jsonObject);
          break;
        case "OnCameraToMoveEnd": //镜头移动动作结束；
          this.cloudRender
            .SuperAPI("RemoveCovering", {
              id: digitalTwinApi.poiId || this.poiId, //覆盖物id
              covering_type: "poi", //覆盖物类型, 详见下表
            })
            .then((_back) => {
              console.log(_back);
            });
          window.cloudRender
            .SuperAPI("RemoveCovering", {
              id: ["path_xhz", "patrol", "1747452955821404160", "100", "100"], //覆盖物id
              covering_type: "path", //覆盖物类型, 详见下表
            })
            .then((_back) => {
              console.log("删除路径", _back);
            });
          this.cameraMoveState();
          this.$emit("showOpen", false);
          //隐藏小人模型
          window.cloudRender
            .SuperAPI("ShowHideAESObject", {
              eid: ["-9111626350604376433"],
              bshow: false, //true:显示; false:隐藏
            })
            .then((_back) => {
              console.log(_back);
            });
          break;
        case "OnSwitchChinaMapSuccess": //开启中国地图成功
          //this.$emit('OnSwitchChinaMapSuccess');
          break;
        case "OnSceneEffectClick": //5G圆形特效点击事件
          //this.$emit('OnSceneEffectClick');
          if (jsonObject.args && jsonObject.args.id) {
            if (jsonObject.args.id == "youche_zm_effect_id") {
              // this.initDiaLog({
              //   data: "http://192.168.1.30:7100/minio/uavfile/2024/VR.mp4",
              //   title: "VR线上培训",
              //   name: "videolog",
              // });
              // this.dialogFull = true;
              this.infoLog.resultshow = true;
              this.infoLog.id = "vrvideo";
              this.infoLog.data = "http://192.168.1.30:7100/minio/uavfile/2024/VR.mp4";

            }
            if (jsonObject.args.id == "hssk_cameraroaming_effect_id") {
              this.hsskCamreraRoamingPlay();
            }
          }
          break;
        //  场景响应鼠标、键盘操作 操作开始
        case "OnSuperAPIInputActionStart":
          if (jsonObject.args) {
            this.inputActionStartFunction(jsonObject.args);
          }
          break;
        //  场景响应鼠标、键盘操作 操作结束
        case "OnSuperAPIInputActionEnd":
          break;
        case "OnStartGetCoordSuccess":
          console.log("OnStartGetCoordSuccess", "开启地图点位信息成功", jsonObject);
          break;
        case "OnGetCoord":
          console.log("OnGetCoord", "获取地图点位信息成功", jsonObject);
          if (jsonObject.args) {
            let args = jsonObject.args;
            if (args.coord_result && args.coord_result.length > 0) {
              let coords = [];
              args.coord_result.forEach((item) => {
                coords.push({
                  coord: item.coord,
                });
              });
              console.log("coords", coords);
              this.$store.commit("common/SET_POINT", coords.pop());
            }
          }
          break;
        case "OnCoverToMoveStart":
          break;
        case "OnCoverToMoveEnd":
          break;
        case "OnWimWFSFileDownLoadSuccess":
          console.log(
            "OnWimWFSFileDownLoadSuccess, 加载演进初始数据",
            jsonObject,
            jsonObject.args.GridID.substring(5)
          );
          // this.waterFloodChangeGrid(
          //   jsonObject.args.GridID.substring(5),
          //   jsonObject.args.GridID
          // );
          break;
      }
      return data;
    },
    // 工具集--修改场景天气
    switchWeather(weather) {
      if (weather) {
        this.cloudRender.SuperAPI("SetEnvWeather", {env_weather: weather}, (statue) => {
          console.log("SetEnvWeather", weather, statue);
        });
      } else {
        if (this.digitalCon.weatherCityCode) {
          this.getWeatherInfo(this.digitalCon.weatherCityCode);
        } else {
          this.cloudRender.SuperAPI(
            "SetEnvWeather",
            {env_weather: "Sunny"},
            (statue) => {
              console.log("SetEnvWeather", "Sunny", statue);
            }
          );
        }
      }
    },
    // 工具集--修改场景时间
    switchDate(date) {
      if (date != null) {
        this.cloudRender.SuperAPI("SetEnvTime", {env_time: date}, (status) => {
          console.log("SetEnvTime", date, status); //成功、失败回调
        });
      } else {
        let now = new Date();
        let dateStr = now.getHours() + ":" + now.getMinutes();
        this.cloudRender.SuperAPI("SetEnvTime", {env_time: dateStr}, (status) => {
          console.log("SetEnvTime", dateStr, status); //成功、失败回调
        });
      }
    },
    // 工具集--限制场景镜头视界
    setCameraSpace() {
      if (
        this.digitalCon.cameraSpace &&
        this.digitalCon.cameraSpace.points &&
        this.digitalCon.cameraSpace.points.length > 0
      ) {
        digitalTwinApi.setCameraSpace(this.digitalCon.cameraSpace);
      }
    },
    // 工具集--解除场景镜头视界限制
    resetCameraSpace(state) {
      digitalTwinApi.resetCameraSpace(state);
    },
    // 工具集--开启地图标点  coordType: 坐标类型(0:经纬度坐标, 1:cad坐标)
    startGetCoord(coordType, cadMapKey, coordZType, coordinateShow, iconShow) {
      let jsondata = {
        coord_type: coordType || 0,
        cad_mapkey: cadMapKey || "",
        coord_z_type: coordZType || 0,
        coordinate_show: coordinateShow || false,
        icon_show: iconShow || true,
      };
      this.cloudRender.SuperAPI("StartGetCoord", jsondata).then((e) => {
        console.log("StartGetCoord", e);
      });
    },
    // 工具集--结束地图标点
    endGetCoord() {
      this.cloudRender.SuperAPI("EndGetCoord");
    },
    // 工具集--设置场景渲染质量 low medium high epic
    setRenderQuality(quality) {
      let jo = {
        quality: quality,
      };
      this.cloudRender.SuperAPI("SetRenderQuality", jo, (statue) => {
        console.log("SetRenderQuality", statue);
      });
    },
    // 工具集--设置镜头绕场景中心旋转 time 相机绕一周时间(秒) direction clockwise顺时针  anticlockwise:逆时针  stop:停止旋转
    setCameraRotate(time, direction) {
      let jo = {
        time: time,
        direction: direction,
      };
      this.cloudRender.SuperAPI("SetCameraRotate", jo, (e) => {
      });
    },
    //获取场景相机参数
    getCameraParams() {
      let that = this;
      let jsondata = {
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "", //CAD基准点Key值, 项目中约定
      };
      that.cloudRender.SuperAPI("GetCameraInfo", jsondata).then((e) => {
        console.log("相机参数", e);
        let jo = JSON.parse(e);
        let args = jo.args;
        console.log("相机参数 args", args);
        if (args != null) {
          that.digitalCon.firstMapViewerOption = args;
          that.digitalCon.firstMapViewerOption.fly = false;
        }
      });
    },
    // 演进相关
    // 淹没演进工具集  2.淹没格网转置
    waterFloodChangeGrid(data, name) {
      let jsondata = {
        GridID: name, //网格id，自主命名，需与下载id一致
      };
      setTimeout(() => {
        // this.queryForm.schemeName = uuidv4();
        this.initWS(this.queryForm);
      })
      this.cloudRender.SuperAPI("WimWFSSetData", jsondata, (e) => {
        console.log("淹没格网转置:", e); //成功、失败回调
        // this.queryForm.schemeName = uuidv4();
        // this.initWS(this.queryForm);
      });
    },
    //预演进度改为WebSocket
    initWS(formData) {
      let origin = window.location.origin
      const basicPath = process.env.VUE_APP_BASE_API;
      let websocketUrl = ''
      if (process.env.NODE_ENV === 'development') {
        websocketUrl = `ws://172.20.2.6:28089?token=${getToken()}`
      } else {
        websocketUrl = `${origin}${basicPath}/_proxy/ws://172.20.2.6:28089?token=${getToken()}`
      }
      websocketUrl = `ws://60.208.119.38:18003/prod-api/_proxy/ws://172.20.2.6:28089?token=${getToken()}`
      this.socket = new WebSocket(websocketUrl);
      this.socket.onopen = () => {
        console.log("WebSocket 连接已经建立。");
        this.totalPage = 0;
        this.tmidxList = [];
        this.sockeData = []
        let form = {
          actionType: this.queryForm.actionType,
          content: {
            ...this.queryForm,
          },
        };
        this.socket.send(JSON.stringify(form));
      };
      //接收数据
      this.socket.onmessage = (event) => {
        console.log("接收到消息onmessage: ", event);
        const {
          totalPage,
          tmidxList,
          crtTmidx,
          floodGridData,
          GridID,
          viewName,
          plcd,
          actionType
        } = JSON.parse(event.data);
        // this.$parent.processShow = true;
        if (totalPage) {
          this.totalPage = totalPage;
          this.tmidxList = tmidxList;
          this.viewName = viewName;
          this.plcd = plcd;
        } else {
          if (actionType == 1012) {
            this.waterFloodDataUpload(this.sockeData, GridID);
          } else {
            this.$parent.processShow = true;
            this.flatData(floodGridData)
            this.sockeData.push(floodGridData);
          }
        }
        console.log("收到服务器消息：", event.data);
      };
      this.socket.onerror = (event) => {
        console.error("WebSocket 连接出现错误：", event);
        this.$parent.processShow = false;
        this.$parent.percentage = 0;
      };
      this.socket.onclose = () => {
        console.log("WebSocket 连接已经关闭。");
      };
    },
    //处理进程数据数据
    flatData(data) {
      this.cloudRender.SuperAPI(
        "WimWFSDataLoad",
        data,
        (status) => {
          let data = JSON.parse(status)
          console.log(data, "data1111");
          const {TimeIndex} = data.args
          let currentPerc = this.totalPage
            ? (TimeIndex / this.totalPage) * 100
            : 0;
          this.$parent.percentage = currentPerc > 100 ? 100 : Number(currentPerc.toFixed(1))
          if (this.totalPage == TimeIndex) {
            this.$parent.percentage = 100
            this.$modal.msgSuccess("推演数据数据计算完成");
            this.$store.commit("app/SET_QUERY_DATA", {
              viewName: this.viewName,
              plcd: this.plcd,
              tmidxListLength: this.tmidxList.length
            });
            this.$parent.processShow = false;
            setTimeout(() => {
              Bus.$emit("rehearsal", true)
            }, 1000)
          }
        }
      );
    },
    // 淹没演进工具集 3.网格数据加载 走接口
    waterFloodDataUpload(res, name) {
      console.log(res, "res123");
      let tmidxList = this.sockeData;
      let viewName = this.viewName;
      let plcd = this.plcd;
      // let tmidxList = res;
      // let viewName = 'v_forecast_r_grid_7';
      // let plcd = '20240905080000';
      this.$store.commit("app/SET_QUERY_DATA", {viewName, plcd, tmidxListLength: tmidxList.length});
      var lengthData = tmidxList.length;
      //尾巴上加一个空帧来隐藏其数据
      let jsondata = {
        GridID: name, //网格id
        //单个时段的网格数据
        FloodGridData: {
          TimeIndex: lengthData + 1, //时间序列帧号
          FloodGridCount: "105359", //网格总数量，默认自动计算
          ValueArray: [], //水深值
          GridIDArray: [], //shp文件的cell ID序列号，从1起计。若cell-ID从0开始需序号加1
        },
      };
      tmidxList.push(jsondata);
      if (res.length) {
        //循环预加载需要预加载的数据
        for (let jj = 0; jj < this.floodParams.length; jj++) {
          if (this.floodParams[jj].isPreLoad) {
            for (
              let kk = 0;
              kk < this.floodParams[jj].preLoadItems.length;
              kk++
            ) {
              if (!this.floodParams[jj].preLoadItems[kk].loaded) {
                this.waterFloodFileLoadAndChangeGrid(
                  this.floodParams[jj].preLoadItems[kk].floodId,
                  this.floodParams[jj].preLoadItems[kk]
                );
                this.floodParams[jj].preLoadItems[kk].loaded = true;
                break;
              }
            }
          }
        }
      }
      let jsondata1 = {
        GridID: name,
        DataMax: "6.0",
        DataMin: "0.0",
        MatBlur: "0.004",
        MatOpacity: "0.7", //热力透明度范围为0-1，0为全透明，1为全不透明
      };

      this.cloudRender.SuperAPI("WimWFSSetFlood", jsondata1, (_back) => {
        console.log(_back, "WimWFSSetFlood");
      });

      let jsondata2 = {
        GridID: name,
        addPositionX: "0",
        addPositionY: "0",
        addScaleX: "0",
        addScaleY: "0",
        addRotation: "0",
        addHeight: "70",
      };
      this.cloudRender.SuperAPI("WimWFSSetPosition", jsondata2, (_back) => {
        console.log(_back);
      });
      console.log("成功加载演进数据集", name);
    },
    // 淹没演进工具集 8.淹没效果清除
    waterFloodClear(name) {
      let jsondata = {
        GridID: name, //清除效果的网格id
      };
      this.cloudRender
        .SuperAPI("WimWFSClearFloodEffect", jsondata)

        .then((e) => {
          console.log("淹没效果清除:", e); //成功、失败回调
          // this.waterFloodDelateGrid(name);
        });
    },
    // 淹没演进工具集 一帧一帧跑
    waterFloodPlayOne(control, data, name) {
      console.log("跑数据的名称", name, "时序数", data);
      let jsondata4 = {
        GridID: name, //网格id，自主命名，需与下载id一致
        waterMatIndex: "0", //水体材质样式，8种，编号0-7
        HeatMapMatIndex: "0", //热力图样式， 0正常，1等值线
        PlaySpeed: "2", //播放速度设定,1为8s一帧，2为4s一帧
        PlayIndex: data, //从指定index开始播放
        Play: "false", //播放true or 暂停false，暂停为暂停在index帧
        HeatMap: control, //是否是热力图，true为是，false为否
      };
      this.cloudRender.SuperAPI("WimWFSPlayFlood", jsondata4, (_back) => {
        console.log("帧数动了");

      });
    },
    // 淹没演进工具集  1.配置文件下载
    waterFloodFileLoadAndChangeGrid(name, option) {
      let that = this;
      let jsondata = {
        GridID: name, //网格id，自主命名
        TifURL: option.floodBasicData.tifURL + ".tif", //本地或在线地址
        ShpURL: option.floodBasicData.shpURL + ".shp", //本地或在线地址
        ShxURL: option.floodBasicData.shxURL + ".shx", //本地或在线地址
        PrjURL: option.floodBasicData.prjURL + ".prj", //本地或在线地址
        DbfURL: option.floodBasicData.dbfURL + ".dbf", //本地或在线地址
        isLocalPath: option.isLocalfloodBasicData, //是否本地路径，true为本地，false为非本地，上述地址两种方式只取一种
      };
      console.log("文件下载", jsondata);
      this.cloudRender.SuperAPI("WimWFSFileDownLoad", jsondata, (_back) => {
        let data = JSON.parse(_back);
        console.log(JSON.stringify(data, null, 2));
        // that.waterFloodChangeGrid();
      });
    },
    //添加村庄点位信息
    addCZPoint(num) {
      axios
        .get("/json/CZZYData.json")
        .then((response) => {
          let list = response.data.args.coord_result;
          let customPoiList = [];
          // list.forEach((item) => {
          var item = list[num];
          let labelList = item.label;
          console.log(labelList, "labelList");
          var dataUrl = null;
          if (
            num == 4 ||
            num == 5 ||
            num == 6 ||
            num == 7 ||
            num == 9 ||
            num == 11
          ) {
            dataUrl =
              "https://www.zhywater.com:18190/static/sylogo//yx_ymd1.png";
          } else {
            dataUrl = "https://www.zhywater.com:18190/static/sylogo//yx_ymd.png";
          }
          let content = [];
          if (labelList && labelList.length > 0) {
            for (let i = 0; i < 1; i++) {
              let label = labelList;
              console.log(label.length, "label.length");
              content.push({
                text: [label, "#fff", "12"],
                text_offset: "10," + (5 + i * 20),
                text_centered: false,
                text_boxwidth: label.length * 16,
                scroll_speed: 1,
              });
            }
          }
          let point = {
            id: item.id,
            //label: item.label,
            coord: item.coord,
            coord_z: item.coord_z,
            point_type: item.type,
            state: "monitors_state1",
            always_show_label: false,
            show_label_range: "0,20",
            params: {
              nm: item.id.toLowerCase(),
            },
            marker: {
              size: "30,30",
              images: [
                {
                  define_state: "monitors_state1",
                  normal_url: dataUrl, //'http://superapi.51hitech.com/doc-static/images/static/markerNormal.png',
                  activate_url: dataUrl, // 'http://superapi.51hitech.com/doc-static/images/static/markerActive.png'
                },
              ],
            },
            label: {
              bg_size: "100," + content.length * 25,
              bg_offset: "22,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
              content: content,
            },
          };
          customPoiList.push(point);
          this.poiList.push(point);
          // });
          if (customPoiList.length > 0) {
            //console.log(" 添加淹没村庄信息", customPoiList);
            digitalTwinApi.AddCustomPOI(customPoiList);
          }
        })
        .catch((error) => {
          // 处理错误
          console.error("Error loading JSON file:", error);
        });
    },
    //删除村庄点位
    removeCZPoint() {
      var num = [
        "601",
        "602",
        "603",
        "604",
        "605",
        "606",
        "607",
        "608",
        "609",
        "610",
        "611",
        "612",
      ];
      for (let i = 0; i < num.length; i++) {
        let jsondata = {
          id: num[i], //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
        });
      }
    },
    //删除单个村庄点位
    removeCZOnePoint(num) {
      //num是数组
      for (let i = 0; i < num.length; i++) {
        let jsondata = {
          id: num[i], //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
        });
      }
    },
    //初始化添加自定义poi点
    addCustomPOI1(num) {
      var list = [
        {
          index: "jc1-0",
          coord: "117.391983,37.838116",
          name: [
            "淹没村庄：后张村",
            "转移地点：安居社区",
            "村落人口数量：153人",
            "联系人电话：19862263341",
          ],
          coord_z: "15.100923",
          type: "3",
        },
        {
          index: "jc1-1",
          coord: "117.711922,38.075054",
          name: [
            "淹没村庄：牛东村",
            "转移地点：大山瑞东小区",
            "村落人口数量：347人",
            "联系人电话：15206426993",
          ],
          coord_z: "24.357529",
          type: "3",
        },
        {
          index: "jc1-2",
          coord: "117.467995,37.91848",
          name: [
            "淹没村庄：大麻湾村 ",
            "转移地点：杨家村",
            "村落人口数量：205人",
            "联系人电话：15606726833",
          ],
          coord_z: "24.357529",
          type: "3",
        },
        {
          index: "jc1-3",
          coord: "117.481499,37.910622",
          name: [
            "淹没村庄：前庄科村",
            "转移地点：大淀村",
            "村落人口数量：401人",
            "联系人电话：13245863921",
          ],
          coord_z: "24.357529",
          type: "3",
        },
        {
          index: "jc1-4",
          coord: "117.444931,37.848789",
          name: [
            "淹没村庄：大黄邱村",
            "转移地点：前柳村",
            "村落人口数量：753人",
            "联系人电话：13245553921",
          ],
          coord_z: "24.357529",
          type: "3",
        },
        {
          index: "jc1-5",
          coord: "117.563072,38.04303",
          name: [
            "淹没村庄：小泊头西村",
            "转移地点：张义井村",
            "村落人口数量：531人",
            "联系人电话：13245898921",
          ],
          coord_z: "24.357529",
          type: "3",
        },
      ];
      var item = list[num];
      // list.forEach((item) => {
      var photoType = "";
      switch (item.type) {
        case "1":
          photoType =
            "https://www.zhywater.com:18190/static/sylogo/yx_green.png";
          break;
        case "2":
          photoType =
            "https://www.zhywater.com:18190/static/sylogo/yx_yellow.png";
          break;
        case "3":
          photoType = "https://www.zhywater.com:18190/static/sylogo/yx_red.png";
          break;
      }
      console.log(item, "aa");
      let jsondata = {
        id: item.index,
        coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
        cad_mapkey: "",
        adaptive: true, //true:自适应大小;false:默认
        coord: item.coord,
        coord_z: item.coord_z,
        coord_z_type: "2", //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        always_show_label: true, //是否永远显示label, true:显示label(默认), false:不显示label
        show_label_range: "0,2000", //POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:always_show_label属性优先于此属性)
        sort_order: true,
        animation_type: "wipe",
        duration_time: 0.1,
        state: "bubble_state_1",
        marker: {
          size: "52,75", //marker大小(宽,高 单位:像素)
          images: [
            {
              define_state: "bubble_state_1",
              normal_url:
                "https://www.zhywater.com:18190/static/sylogo/yx_jt1.png",
              activate_url:
                "https://www.zhywater.com:18190/static/sylogo/yx_jt1.png",
            },
          ],
        },
        label: {
          bg_image_url: photoType,
          bg_size: "210,115", //label大小(宽, 高 单位:像素)
          bg_offset: "-60,150", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
          content: [
            {
              text: [item.name[0], "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,11", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: [item.name[1], "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,30", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: [item.name[2], "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,50", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
            {
              text: [item.name[3], "ffffff", "10"], //[文本内容, HEXA颜色, 文本大小]
              text_offset: "10,70", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
              text_boxwidth: 200, //文本框宽度
              text_centered: true, //文本居中(true:居中; false:不居中)
              scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
            },
          ],
        },
        // "window":{
        //     "url":"http://superapi.51hitech.com/doc-static/images/static/video.html",
        //     "size":"260,175",      //window大小(宽,高 单位:像素)
        //     "offset":"25,90"      //window左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
        // }
      };
      this.cloudRender.SuperAPI("AddCustomPOI", jsondata, (status) => {
        console.log("AddCustomPOI", status); //成功、失败回调
      });
      // });
      // })
      // .catch((error) => {
      //   console.error(error);
      // });
    },
    //更新poi点label
    updateCustomPOILabel(data) {
      var data = [
        {id: "jc1-0", name: ["流量：517", "水位：117"], type: "1"},
        {id: "jc1-1", name: ["流量：517", "水位：117"], type: "2"},
        {id: "jc1-2", name: ["流量：517", "水位：117"], type: "3"},
        {id: "jc1-3", name: ["流量：517", "水位：117"], type: "2"},
      ];
      //   console.log(data,"data");
      data.forEach((item) => {
        console.log("update");
        var photoType = "";
        switch (item.type) {
          case "1":
            photoType =
              "https://www.zhywater.com:18190/static/sylogo/yx_green.png";
            break;
          case "2":
            photoType =
              "https://www.zhywater.com:18190/static/sylogo/yx_yellow.png";
            break;
          case "3":
            photoType =
              "https://www.zhywater.com:18190/static/sylogo/yx_red.png";
            break;
        }
        let jsondata = {
          id: item.id,
          label: {
            bg_image_url: photoType,
            bg_size: "120,65", //label大小(宽, 高 单位:像素)
            bg_offset: "-60,110", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
            content: [
              {
                text: [item.name[0], "ffffff", "14"], //[文本内容, HEXA颜色, 文本大小]
                text_offset: "10,11", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
                text_boxwidth: 100, //文本框宽度
                text_centered: true, //文本居中(true:居中; false:不居中)
                scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
              },
              {
                text: [item.name[1], "ffffff", "14"], //[文本内容, HEXA颜色, 文本大小]
                text_offset: "10,30", //文本框左上角相对于label中bg_offset左上角的margin偏移(x,y 单位:像素), 注: x为正向右, y为正向下
                text_boxwidth: 100, //文本框宽度
                text_centered: true, //文本居中(true:居中; false:不居中)
                scroll_speed: 0, //文本滚动速度(0:不滚动; 注: "text_centered":true优先)
              },
            ],
          },
        };
        this.cloudRender.SuperAPI(
          "UpdateCustomPOILabel",
          jsondata,
          (status) => {
            console.log(status); //成功、失败回调
          }
        );
      });
    },
    //删除poi点
    deletePOILabel(data) {
      var data = [
        {id: "jc1-0"},
        {id: "jc1-1"},
        {id: "jc1-2"},
        {id: "jc1-3"},
        {id: "jc1-4"},
        {id: "jc1-5"},
        {id: "jc1-6"},
        {id: "jc1-7"},
        {id: "jc1-8"},
        {id: "jc1-9"},
        {id: "jc1-10"},
      ];
      data.forEach((item) => {
        let jsondata = {
          id: item.id, //覆盖物id
          covering_type: "poi", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata).then((_back) => {
          console.log(_back);
        });
      });
    },
    //箭头路径
    arrowPath1(num) {
      axios
        .get("/json/CZZYLXData.json")
        .then((response) => {
          // var  num  = 1
          console.log();
          let list = response.data.args.coord_result[num][1];
          let id = response.data.args.coord_result[num][0];
          console.log(list, id, "list");
          let jsondata = {
            id: id,
            advancedSetting: {
              smoothnessOfCorners: "extremelyHigh", //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
            },
            coord_type: 0, //坐标类型(0:经纬度坐标, 1:cad坐标)
            coord_z_type: 2, //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
            cad_mapkey: "", //CAD基准点Key值, 项目中约定
            type: "arrow", //样式类型; 注①
            color: "ff0000", //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
            pass_color: "ffff00", //覆盖物移动经过路径颜色(HEX颜色值)
            width: 30, //宽度(单位:米, 圆柱直径或方柱边长；当类型为“adaptive_solid”，含义为倍率)
            speedRate: 1, //流动特效的移动倍率,仅针对部分类型有效（arrow,arrow_dot,arrow_dashed,brimless_arrow,scan_line,scan_line_solid）
            points: list,
          };
          this.cloudRender.SuperAPI("AddPath", jsondata, (status) => {
            console.log(status); //成功、失败回调
          });
        })
        .catch((error) => {
          // 处理错误
          console.error("Error loading JSON file:", error);
        });
    },
    //删除箭头路径
    deleteArrowPath() {
      var arr = [
        "roadheatmap_id1",
        "roadheatmap_id2",
        "roadheatmap_id3",
        "roadheatmap_id4",
        "roadheatmap_id5",
        "roadheatmap_id6",
        "roadheatmap_id7",
        "roadheatmap_id8",
        "roadheatmap_id9",
        "roadheatmap_id10",
        "roadheatmap_id11",
      ];
      console.log(arr[0], arr[1], "arr[0]");
      for (let i = 0; i < arr.length; i++) {
        let jsondata = {
          id: arr[i], //覆盖物id
          covering_type: "path", //覆盖物类型, 详见下表
        };
        this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
          console.log(status); //成功、失败回调
        });
      }
    },
    //删除单个箭头路径
    deleteArrowOnePath(num) {
      var arr = [
        "roadheatmap_id1",
        "roadheatmap_id2",
        "roadheatmap_id3",
        "roadheatmap_id4",
        "roadheatmap_id5",
        "roadheatmap_id6",
        "roadheatmap_id7",
        "roadheatmap_id8",
        "roadheatmap_id9",
        "roadheatmap_id10",
        "roadheatmap_id11",
      ];
      console.log(arr[0], arr[1], "arr[0]");
      // for (let i = 0; i < arr.length; i++) {
      let jsondata = {
        id: arr[num], //覆盖物id
        covering_type: "path", //覆盖物类型, 详见下表
      };
      this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
        console.log(status); //成功、失败回调
      });
      // }
    },
    //添加三号支沟河流
    addWDRiver() {
      // var geojson
      // switch (id) {
      let jsondata = {
        "id": "shanhaozhigou",
        "advancedSetting": {
          "smoothnessOfCorners": "extremelyHigh"  //设置路径边角的平滑度(extremelyHigh:极高; high:高; middle:中; low:低;)
        },
        "coord_type": 0,                //坐标类型(0:经纬度坐标, 1:cad坐标)
        "cad_mapkey": "",               //CAD基准点Key值, 项目中约定
        "coord_z": -15,                   //高度(单位:米)
        "coord_z_type": 0,              //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        "type": "solid",                //样式类型; 注①
        "color": "0078d4",              //颜色(HEX颜色值, 空值即透明; railway类型无效, 默认黑白色)
        "pass_color": "ffff00",         //覆盖物移动经过路径颜色(HEX颜色值)
        "width": 300,                    //宽度(单位:米, 圆柱直径或方柱边长)
        "speedRate": 1,                 //流动特效的移动倍率,仅针对部分类型有效（arrow,arrow_dot,arrow_dashed,brimless_arrow,scan_line,scan_line_solid）
        "id_field_name": "id",          //指定geojson中的id字段
        //支持json或文件形式、二选一
        "geojson": {
          "type": "FeatureCollection", "features": [
            {
              "type": "Feature", "geometry": {
                "type": "LineString",
                "coordinates": [[118.155724, 37.189781], [118.1606342763, 37.1883382992], [118.1621196695, 37.1875188247], [118.163025461, 37.1876577965], [118.1634320091, 37.1873192783], [118.1639031542, 37.1868666929], [118.1654873222, 37.1859614375], [118.1682876291, 37.1845816821], [118.171104971, 37.1829754146], [118.1753135129, 37.1806169334], [118.1800262121, 37.177917639], [118.1832032626, 37.1761976789], [118.184299439, 37.175394342], [118.185181331, 37.1748644136], [118.1858235285, 37.1744876539], [118.1864477749, 37.1739244249], [118.1873211658, 37.1734614308], [118.1880204357, 37.1732813053], [118.1882965483, 37.1732653499], [118.1886911773, 37.1732808805], [118.1892368697, 37.1733809603], [118.1896842464, 37.1735128287], [118.1902837739, 37.1735008448], [118.1907642786, 37.1734629267], [118.191119173, 37.1733470606], [118.1917899565, 37.1732310481], [118.1925794762, 37.1731885242], [118.1934941714, 37.1731408654], [118.1944482045, 37.1729723289], [118.1952243659, 37.1729198058], [118.1956393787, 37.172919641], [118.1961927197, 37.1730305939], [118.1970190416, 37.173435822], [118.1973898973, 37.1734925555], [118.1979910137, 37.173496143], [118.198359883, 37.1733955191], [118.1988545229, 37.1732416091], [118.1996549622, 37.1729943389], [118.2004150598, 37.1726740014], [118.2013598035, 37.1722179836], [118.202237347, 37.1718576878], [118.2025739295, 37.1716091044], [118.2033521689, 37.1711753809]]
              }, "properties": {"NAME": "未命名线_1"}
            }
          ]
        }
      }
      cloudRender.SuperAPI("AddGeoPath", jsondata, (status) => {
        console.log(status); //成功、失败回调
      })
    },
    //隐藏河流
    hidderRiver(){
      let jo = {
            id: "shanhaozhigou", //覆盖物id
            covering_type: "path", //覆盖物类型, 详见下表
            bshow: false, //true:显示; false:隐藏
          };
          this.cloudRender.SuperAPI("ShowHideCovering", jo, (status) => {
            // console.log(status); //成功、失败回调
          });
    },
    //获取预报-预报成果撒点
    getHydrologicalData() {
      var that = this
      addHydrological().then((res) => {
        console.log(res, "res1111");
        that.addHydrologicalPoint(res.data, "水文站")
      });
      // this.ZWXHTree.checkedKeys.push("ssjk-swzsj")
    },
    // 获取预报-水情撒点
    getAddHydrological2() {
      let params = {
        "sttp": 1,
      }
      addHydrological2(params).then((res) => {
        this.addHydrologicalPoint(res.data)
      })
    },
    // 获取预报-雨情撒点
    getAddHydrological3() {
      let params = {
        "sttp": 6,
      }
      addHydrological2(params).then((res) => {
        this.addHydrologicalPoint(res.data)
        this.RainfallData=res.data
      })
    },
    //获取预警-预报预警撒点
    getGJTideLevelData() {
      var that = this
      addGJHydrological().then((res) => {
        console.log(res, "res444");
        that.addHydrologicalPoint(res.data, "潮位站")
      });
      console.log();

      // this.ZWXHTree.checkedKeys.push("ssjk-cwz")
    },
    //获取预警-实测预警撒点
    getSSGJTideLevelData() {
      var that = this
      let params = {
        "sttp": 1,
      }
      addSSGJHydrological(params).then((res) => {
        console.log(res, "res555");
        that.addHydrologicalPoint(res.data, "潮位站")
      });
      console.log();

      // this.ZWXHTree.checkedKeys.push("ssjk-cwz")
    },
    //获得抢险队伍撒点
    getSecureTeamPoint(){
      var that = this
      getsecureTeam().then((res) => {
        console.log(res, "res666");
        that.addHydrologicalPoint(res.data)
      });
    },
    //获得仓库点
    getWareHousePoint(){
      var that = this
      getwareHouse().then((res) => {
        console.log(res, "res777");
        that.addHydrologicalPoint(res.data)
      });
    },
    //添加撒点
    addHydrologicalPoint(data) {
      console.log(data, "data");

      var that = this
      if (data.length == 0) {
        return
      }
      let customPoiList = [];
      var list = data
      var always_show_label =true
      list.forEach((item) => {
        if (!item.type) {
          return
        }
        // that.ZDData.push(item)
                let labelList = item.type;
                let content = [];
                let textData = []
                let textSize = []
                let size = []
                switch (item.type) {
                    case"ssjk-swzsj":
                    textData=[item.label]
                    textSize=[100]
                    break;
                    case"ssjk-swz":
                    textData=[item.label]
                    textSize=[100]
                    break;
                    case "fybz-qxdw":
                    case"fybz-wzck":
                    textData=[item.label]
                    textSize=[150]
                    break;
                    case"ssjk-swzsc":
                    textData=[item.label]
                    textSize=[100]
                    break;

                    case"ssjk-cwz":
                    textData=["潮位站:"+item.label]
                    textSize=[160]
                    break;
                    case"ssjk-spd":
                    textData=["视频点:"+item.name]
                    textSize=[120]
                    break;
                    case"ssjk-yqz":
                    textData=["雨量站:"+item.label]
                    textSize=[300]
                    always_show_label =false
                    break;
                }
                if (textData.length == 0) {
                  size = "1,1"
                }
                else{
                  switch (textData.length) {
                    case 1:
                      size = textSize[0]+",30"
                      break;
                      case 2:
                      size = textSize[0]+",50"
                      break;
                      case 3:
                      size = textSize[0]+",70"
                      break;
                      case 4:
                      size = textSize[0]+",90"
                      break;
                      case 5:
                      size = textSize[0]+",110"
                      break;
                      case 6:
                      size = textSize[0]+",130"
                      break;
                      case 7:
                      size = textSize[0]+",150"
                      break;
                      case 8:
                      size = textSize[0]+",170"
                      break;
                      case 9:
                      size = textSize[0]+",190"
                      break;
                      case 10:
                      size = textSize[0]+",210"
                      break;
                    default:
                      break;
                  }
                }
                if (labelList) {
                    let label = labelList;
                    content.push({
                      // text: [label, "#fff", "10"],
                       text: [textData[0], "#fff", "12"],
                      text_offset: "10,5",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[1], "#fff", "12"],
                      text_offset: "10,25",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[2], "#fff", "10"],
                      text_offset:"10,45",
                      text_centered: false,
                      text_boxwidth: 500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[3], "#fff", "10"],
                      text_offset:"10,65",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[4], "#fff", "10"],
                      text_offset:"10,85",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[5], "#fff", "10"],
                      text_offset:"10,105",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[6], "#fff", "10"],
                      text_offset:"10,125",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[7], "#fff", "10"],
                      text_offset:"10,145",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[8], "#fff", "10"],
                      text_offset:"10,165",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[9], "#fff", "10"],
                      text_offset:"10,185",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },
                    {
                      // text: [label, "#fff", "10"],
                       text: [textData[10], "#fff", "10"],
                      text_offset:"10,205",
                      text_centered: false,
                      text_boxwidth:  500,
                      scroll_speed: 1,
                    },);
                }
                var urlLabel = item.type
                if (item.alarm!=undefined&&item.alarm) {
                  urlLabel = urlLabel +"-alarm"
                }
               let coord = item.lat+","+item.lon
                if (item.type =="fybz-wzck"||item.type =="fybz-qxdw") {
                  coord = item.lon+","+item.lat
                }
                let point = {
                  id: item.id,
                  //label: item.label,
                  coord: coord,
                  coord_z: "0",
                  point_type: item.type,
                  labelData:item.label,
                  channel_id:item.id,
                  device_id:item.id,
                  state: "monitors_state1",
                  always_show_label: always_show_label,
                  show_label_range: "0,5000",
                  // params: {
                  //   nm: item.latitude.toLowerCase(),
                  // },
                  marker: {
                    size: "30,30",
                    images: [
                        {
                          define_state: "monitors_state1",
                          normal_url:
                            "https://www.zhywater.com:18190/static/shzglogo//"+urlLabel+".png", //'http://superapi.51hitech.com/doc-static/images/static/markerNormal.png',
                          activate_url:
                            "https://www.zhywater.com:18190/static/shzglogo//"+urlLabel+".png", // 'http://superapi.51hitech.com/doc-static/images/static/markerActive.png'
                        },
                      ],
                  },
                  label: {
                    bg_size: size,
                    bg_offset: "22,35", //整个label左上角相对于marker的中心点(coord坐标中心点)的偏移(x,y 单位:像素), 注: x为正向右, y为正向上
                    content: content,
                  },
                };
                customPoiList.push(point);
                this.SHZGSYpoiList.push(point);
                // this.YJYBpoiList.push(point);
                console.log(point,"point111");

      });
      if (customPoiList.length > 0) {
        digitalTwinApi.AddCustomPOI(customPoiList);
      }
    },
    deleteYJYBPoint() {
      var list = this.SHZGSYpoiList
      this.SHZGSYpoiList = []
      for (let i = 0; i < list.length; i++) {
        let jsondataTest = {
          "id": list[i].id,            //覆盖物id
          "covering_type": "poi",    //覆盖物类型, 详见下表
        }
        this.cloudRender.SuperAPI("RemoveCovering", jsondataTest, (status) => {
          console.log(status); //成功、失败回调
        })
      }
    },
    // 视角跳转 1: 默认视角 2: 全揽视角
    jumpScreenViews(num) {
      switch (num) {
        case 1:
          this.setDefaultView(this.digitalCon.firstMapViewerOption)
          break
        case 2:
          const json = {
            arm_distance: 3386.251,
            cad_mapkey: "",
            center_coord: "118.096971,37.191727",
            coord_type: 0,
            coord_z: "335.079987",
            fly: true,
            isDefault: false,
            label: "全揽视角",
            pitch: 50.539551,
            yaw: 13.0,
          }
          this.cloudRender.SuperAPI("SetCameraInfo", json).then((_back) => {
          });
          break
          case 3:
          const jsondata = {
            arm_distance: 9999.99707,
            cad_mapkey: "",
            center_coord: "118.127211,37.198200",
            coord_type: 0,
            coord_z: "335.079987",
            fly: true,
            isDefault: false,
            label: "雨量点",
            pitch: 56.366943,
            yaw: 23.0,
            fly:true
          }
          this.cloudRender.SuperAPI("SetCameraInfo", jsondata).then((_back) => {
          });
          break
      }
    },
    //添加圆形区域轮廓
    addCircleRegion(radius) {
      this.RainfallData.forEach((item) => {
        console.log(item,"item11");
        let color
        if (item.ssdrp>10) {
          color="7a64004D"
        } else{
          color="2eb6ff4D"
        }
        let jsondata = {
        "id":item.id+1,
        "coord_type": 0,                        //坐标类型(0:经纬度坐标, 1:cad坐标)
        "cad_mapkey": "",                       //CAD基准点Key值, 项目中约定
        "coord_z": 20,                           //高度(单位:米)
        "coord_z_type": 0,                      //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2:相对3D世界海拔; 注:cad坐标无效)
        "center": item.lat+","+item.lon,                         //圆心坐标 lng,lat
        "type": "box_solid_line",                //样式类型; 注①
        "color": color,                    //轮廓颜色(HEXA颜色值)
        "range_height": 30,                     //围栏高度(单位:米)
        // "stroke_weight": 1,                    //底部轮廓线宽度(单位:米)
        "fill_area": "solid",                    //底部区域填充类型; 注②
        "radius": radius                       //半径(单位:米)
      }
      this.cloudRender.SuperAPI("AddCircularRange", jsondata, (status) => {
        this.add3DText(item.lat+","+item.lon,item.id+2,color,item.ssdrp)
        console.log(status); //成功、失败回调
      })
      })


    },
    //更新圆形区域轮廓颜色
    updateCircleRegion(id, color) {
      let jsondata = {
        "id": id,
        "type": "box_solid_line",                //样式类型; 注①
        "color": color,                //颜色(HEXA颜色值)
        "range_height": 30,                 //围栏高度(单位:米)
        // "stroke_weight":10,                //底部轮廓线宽度(单位:米)
        "fill_area": "solid"                //底部区域填充类型; 注②
      }
      this.cloudRender.SuperAPI("UpdateCircularRangeStyle", jsondata, (status) => {
        console.log(status); //成功、失败回调
      })
    },
    //删除圆形区域轮廓
    deleteRegion(id,range) {
      let jsondata = {
        "id": id,            //覆盖物id
        "covering_type": range,    //覆盖物类型, 详见下表
      }
      this.cloudRender.SuperAPI("RemoveCovering", jsondata, (status) => {
        console.log(status); //成功、失败回调
      })
    },
    add3DText(coord, id, color, num) {
      console.log(coord, id, color, num, "coord, id, color, num");
      let jsondata = {
        "id": id,
        "coord_type": 0,                            //坐标类型(0:经纬度坐标, 1:cad坐标)
        "cad_mapkey": "",                           //CAD基准点Key值, 项目中约定
        "coord": coord,        //坐标 lng,lat
        "coord_z": 260,                              //高度(单位:米)
        "coord_z_type": 0,                          //坐标高度类型(0:相对3D世界表面; 1:相对3D世界地面; 2:相对3D世界海拔; 注:cad坐标无效)
        "text": "实时雨量:" + num + "mm",                           //文字内容
        "color": color,                          //颜色(HEX颜色值)
        "size": 300,                                 //字体大小(单位:米)
        "thickness": 10,                            //厚度(单位:米)
        "type": "plain",                            //样式(plain; reflection; metal)
        "outline": 1,                               //轮廓(单位:百分比), 取值范围[0~1]
        "portrait": false,                          //纵向(true/false)
        "space": 0.2,                               //间距(单位:米)
        "flash": 5,                                 //闪烁动效(单位:秒)
        "bounce": 1,                                //反弹动效(单位:米/秒)
        "pitch": 0,                                 //俯仰角(-90~90)
        "yaw": 0,                                   //偏航角(0正北, 0~360)
        "face_to_camera": true                      //文字是否跟踪朝向摄像机(注:true优先, "pitch", "yaw" 无效)
      }                 //坐标高度类型(0:相对3D世界表面；1:相对3D世界地面；2
      this.cloudRender.SuperAPI("Add3DText", jsondata, (status) => {
        console.log(status); //成功、失败回调
      })
    },
    //更新文字颜色样式
    update3DText(id, color, num) {
      let jsondata = {
        "id": id,
        "text": "实时雨量:" + num + "mm",                            //文字内容
        "color": color,                          //颜色(HEX颜色值)
        "size": 300,                                 //字体大小(单位:米)
        "thickness": 10,                            //厚度(单位:米)
        "type": "plain",                            //样式(plain; reflection; metal)
        "outline": 1,                               //轮廓(单位:百分比), 取值范围[0~1]
        "portrait": false,                          //纵向(true/false)
        "space": 0.2,                               //间距(单位:米)
        "flash": 5,                                 //闪烁动效(单位:秒)
        "bounce": 1,                                //反弹动效(单位:米/秒)
        "pitch": 0,                                 //俯仰角(-90~90)
        "yaw": 0,                                   //偏航角(0正北, 0~360)
        "face_to_camera": true                      //文字是否跟踪朝向摄像机(注:true优先, "pitch", "yaw" 无效)
      }
      this.cloudRender.SuperAPI("Update3DTextStyle", jsondata, (status) => {
        console.log(status); //成功、失败回调
      })
    }
  },
  mounted() {
    Bus.$on("titleBrother", this.handleValue);
    //洪水演进计算
    Bus.$on("evolution", (form) => {
      this.$parent.processShow = true;
      this.$parent.percentage = 0;
      this.waterFloodClear("f1000");
      this.queryForm.schemeName = form.schemeName;
      this.queryForm.actionType = form.actionType;
      this.queryForm.schemeCode = form.schemeCode ? form.schemeCode : "";
      this.queryForm.inputs = form.inputs
      setTimeout(() => {
        this.waterFloodChangeGrid("f1000", "f1000");
      }, 300);
    })
    Bus.$on('clearFlood', () => {
      this.waterFloodClear('f1000')
    })
  },
  destroyed() {
    this.cloudRender.SuperAPI("StopRenderCloud"); //关闭云渲染, 释放资源 (此处是关键。单页应用释放资源请注意)
  },
  beforeDestroy() {
    Bus.$off("titleBrother", this.handleValue);
    Bus.$off('evolution');
  },
};

function getAngle(lng_a, lat_a, lng_b, lat_b) {
  var a = ((90 - lat_b) * Math.PI) / 180;
  var b = ((90 - lat_a) * Math.PI) / 180;
  var AOC_BOC = ((lng_b - lng_a) * Math.PI) / 180;
  var cosc = Math.cos(a) * Math.cos(b) + Math.sin(a) * Math.sin(b) * Math.cos(AOC_BOC);
  var sinc = Math.sqrt(1 - cosc * cosc);
  var sinA = (Math.sin(a) * Math.sin(AOC_BOC)) / sinc;
  var A = (Math.asin(sinA) * 180) / Math.PI;
  var res = 0;
  if (lng_b > lng_a && lat_b > lat_a) res = A;
  else if (lng_b > lng_a && lat_b < lat_a) res = 180 - A;
  else if (lng_b < lng_a && lat_b < lat_a) res = 180 - A;
  else if (lng_b < lng_a && lat_b > lat_a) res = 360 + A;
  else if (lng_b > lng_a && lat_b == lat_a) res = 90;
  else if (lng_b < lng_a && lat_b == lat_a) res = 270;
  else if (lng_b == lng_a && lat_b > lat_a) res = 0;
  else if (lng_b == lng_a && lat_b < lat_a) res = 180;
  return res;
}
</script>

<style lang="less" scoped>
@text-color: #00ffff;
@time: 2.5s;

.ue4map {
  width: 100%;
  height: 100%;
  position: absolute;

  // transform-origin: 0 0;
  .bj {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-image: url("~@/assets/images/digitalTwin/beijing.png") !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
  }

  #player {
    width: 100% !important;
    height: 100% !important;
    bottom: 0 !important;
    left: 0 !important;

    video {
      height: 100%;
      width: 100%;
    }

    .close {
      position: absolute;
      right: 4vh;
      top: 7vh;
      z-index: 12;
      color: #fff;
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0.5) !important;
      border-color: #aad4c1 !important;
    }

    /deep/ .el-button + .el-button {
      margin: 0;
    }

    .close:hover {
      opacity: 0.7;
    }

    > img {
      position: absolute;
      top: 6vw;
      right: 1vw;
      z-index: 11;
      width: 20.6vw;
    }

    //.byq{
    //    width: 25.6vw;
    //    //height: 36.4vh;
    //}
    //.zhdq{
    //    width: 25.6vw;
    //    //height: 36.4vh;
    //}
    .left {
      left: 1vw;
      top: 6vw;
      position: absolute;
      width: 20.6vw;
      height: 80vh;
      display: flex;
      flex-direction: column;

      img {
        width: 100%;
        margin-bottom: 2vh;
      }
    }

    .right {
      right: 1vw;
      top: 6vw;
      position: absolute;
      width: 20.6vw;
      height: 80vh;
      display: flex;
      flex-direction: column;

      img {
        width: 100%;
        margin-bottom: 2vh;
      }
    }
  }

  .shade {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: #000000;
    //z-index: 1;
  }

  .move {
    animation: mymove 5s linear infinite;
    -moz-animation: mymove 5s linear infinite;
    -o-animation: mymove 5s linear infinite;
    -webkit-animation: mymove 5s linear infinite;
  }

  @keyframes mymove {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .loadingText {
    font-size: x-large;
    margin: 10% 0 10% 30%;
    font-weight: normal;
    font-stretch: normal;
    color: @text-color;
  }
}

::v-deep .el-progress__text {
  color: @text-color;
}

.loading {
  width: 260px;
  height: 11px;
  margin: 0 auto;
  text-align: center;
  background-color: #000000;
  border: solid 1px #46fcd5;
  transform: skew(315deg);
  display: flex;

  span {
    display: inline-block;
    width: 10px;
    height: 100%;
    margin-right: 5px;
    opacity: 0;
    background: #00ffff;
    animation: load 7s ease infinite;

    &:last-child {
      margin-right: 0;
    }

    &:nth-child(1) {
      -webkit-animation-delay: @time;
    }

    &:nth-child(2) {
      -webkit-animation-delay: @time * 2;
    }

    &:nth-child(3) {
      -webkit-animation-delay: @time * 3;
    }

    &:nth-child(4) {
      -webkit-animation-delay: @time * 4;
    }

    &:nth-child(5) {
      -webkit-animation-delay: @time * 5;
    }

    &:nth-child(6) {
      -webkit-animation-delay: @time * 6;
    }

    &:nth-child(7) {
      -webkit-animation-delay: @time * 7;
    }

    &:nth-child(8) {
      -webkit-animation-delay: @time * 8;
    }

    &:nth-child(9) {
      -webkit-animation-delay: @time * 9;
    }

    &:nth-child(10) {
      -webkit-animation-delay: @time * 10;
    }

    &:nth-child(11) {
      -webkit-animation-delay: @time * 11;
    }

    &:nth-child(12) {
      -webkit-animation-delay: @time * 12;
    }

    &:nth-child(13) {
      -webkit-animation-delay: @time * 13;
    }

    &:nth-child(14) {
      -webkit-animation-delay: @time * 14;
    }

    &:nth-child(15) {
      -webkit-animation-delay: @time * 15;
    }
  }
}

@-webkit-keyframes load {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

::v-deep .el-collapse-item__header {
  background: #4d6565;
  opacity: 0.8;
  color: aliceblue;
  padding-left: 5px;
}

::v-deep .el-collapse-item__wrap {
  background: #4d6565;
  opacity: 0.8;
}

::v-deep .el-collapse-item__content {
  padding: 5px 5px 5px 0;
  color: #fff;
}

::v-deep .el-button--medium:first-child {
  margin-left: 10px;
}

#selectStyle {
  width: 150px !important;
}

/deep/ .el-input--small {
  width: 150px !important;
}

/deep/ .el-switch__label * {
  font-size: 16px;
}

/deep/ .el-switch {
  height: 35px;
}

.block {
  display: flex !important;
  justify-content: space-between;
  margin: 0;
  padding: 0;
  align-items: center;
  // width: 210px;
}

/deep/ .popper__arrow {
  left: 30%;
}

/deep/ .el-input__inner {
  height: 42px;
  color: #00ffff;
  font-size: 18px;
}
</style>
<style>
.el-cascader__dropdown {
  width: 100px;
}

.el-cascader-panel {
  width: 100px;
}

.el-cascader-menu {
  min-width: 100px;
  width: 100px;
  background-color: #409eff;
}

.el-cascader-node {
  padding: 0;
}

.el-cascader-menu__wrap {
  height: 200px;
}

/* .el-cascader-menu {
  min-width: 100px !important;
  width: 100px !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #606266;
  border-right: solid 1px #dfe4ed;
}
.el-cascader-node__postfix {
  position: absolute;
  right: 5px;
}
.el-scrollbar__wrap {
  height: 170px !important;
  width: 100px;
}

.el-cascader__dropdown {
  right: 50px;
  margin: 0;
  padding: 0;
  border: 0;
  /* width: 200px; */
/* }
.el-cascader-panel {
  width: 100px !important;
} */
.ue4map .el-loading-spinner .el-loading-text {
  color: #409eff;
  margin: 3px 0;
  font-size: 20px;
}

.ue4map .el-loading-spinner i {
  font-size: 20px;
}

.funcBtnBox {
  width: 300px;
  max-height: 800px;
  overflow-x: auto;
  overflow-y: auto;
  position: fixed;
  top: 85px;
  right: 35px;
  background: #4d6565;
  border-radius: 5px;
  opacity: 0.8;
  padding: 5px;
}

.dt-title {
  background: url(~@/views/screen/polling/newimg/titleBG.png) no-repeat;
  background-size: 100% 100%;
  width: 175px;
  height: 30px;
  z-index: 3;
  position: absolute;
}

.dt-title > div {
  font-size: 20px;
  font-weight: 600;
  background-image: -webkit-linear-gradient(
    top,
    var(--gradientFrColor),
    var(--gradientToColor)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* .select .el-select .el-select--small {
  width: 180px !important;
} */
</style>
