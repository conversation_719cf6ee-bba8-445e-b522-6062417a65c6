import request from '@/utils/request'

// 查询水质监测列表
export function listWaterquality(query) {
  return request({
    url: '/matrix/waterquality/list',
    method: 'get',
    params: query
  })
}
// 查询实时水质监测列表
export function realTimelListWaterquality(query) {
  return request({
    url: '/matrix/waterquality/szList',
    method: 'get',
    params: query
  })
}

// 查询水质监测详细
export function getWaterquality(stcd) {
  return request({
    url: '/matrix/waterquality/' + stcd,
    method: 'get'
  })
}

// 新增水质监测
export function addWaterquality(data) {
  return request({
    url: '/matrix/waterquality',
    method: 'post',
    data: data
  })
}

// 修改水质监测
export function updateWaterquality(data) {
  return request({
    url: '/matrix/waterquality',
    method: 'put',
    data: data
  })
}

// 删除水质监测
export function delWaterquality(stcd) {
  return request({
    url: '/matrix/waterquality/' + stcd,
    method: 'delete'
  })
}
// 获取工程类型
export function listEngineeringType(data) {
  return request({
    url: '/matrix/project/list',
    method: 'get',
    data: data
  })
}
