import request from '@/utils/request'

// 查询位移监测列表
export function listDrift(query) {
  return request({
    url: '/matrix/drift/list',
    method: 'get',
    params: query
  })
}
// 查询最近一个月的列表 http://localhost:9206/drift/list?pageNum=1&pageSize=20
export function listByMonth(query) {
  return request({
    url: '/matrix/drift/list',
    method: 'get',
    params: query
  })
}
// 查询最近一个月的列表 http://localhost:9206/drift/list?pageNum=1&pageSize=20
export function realTimeLListByMonth(query) {
  return request({
    url: '/matrix/drift/wyList',
    method: 'get',
    params: query
  })
}
//实时查询最近的列表
export function realTimelistByMonth(query) {
  return request({
    url: '/matrix/drift/wyList',
    method: 'get',
    params: query
  })
}
// 查询位移监测详细
export function getDrift(did) {
  return request({
    url: '/matrix/drift/' + did,
    method: 'get'
  })
}

// 新增位移监测
export function addDrift(data) {
  return request({
    url: '/matrix/drift',
    method: 'post',
    data: data
  })
}

// 修改位移监测
export function updateDrift(data) {
  return request({
    url: '/matrix/drift',
    method: 'put',
    data: data
  })
}

// 删除位移监测
export function delDrift(did) {
  return request({
    url: '/matrix/drift/' + did,
    method: 'delete'
  })
}
// 获取测站类型
export function listStation(query){
  return request({
    url: '/matrix/drift/liststation',
    method: 'get',
    params: query
  })
}
//查询首次偏移数据
export function listFirstData(query){
  return request({
    url: '/matrix/drift/listfirst',
    method: 'get',
    params: query
  })
}
//查询本次和上次偏移数据
export function listLastData(query){
  return request({
    url: '/matrix/drift/listlast',
    method: 'get',
    params: query
  })
}
// 数据预测
export function predictedData(data) {
  return request({
      url: '/matrix/drift/getPrediction',
      method: 'post',
      data: data
  })
}
// gnss获取
export function listgnss(query) {
  return request({
    url: '/matrix/drift/listgnss',
    method: 'get',
    params: query
  })
}
// devid获取
export function listDevId() {
  return request({
    url: '/matrix/drift/listdev',
    method: 'get',
  })
}
