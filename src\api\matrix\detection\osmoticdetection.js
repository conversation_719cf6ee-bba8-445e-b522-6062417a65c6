import request from '@/utils/request'

// 查询渗压监测列表
export function listSecure(query) {
  return request({
    url: '/matrix/osmometer/list',
    method: 'get',
    params: query
  })
}
// 查询实时渗压监测列表
export function realTimellistSecure(query) {
  return request({
    url: '/matrix/osmometer/syList',
    method: 'get',
    params: query
  })
}

// 查询渗压监测详细
export function getSecure(did) {
  return request({
    url: '/matrix/osmometer/' + did,
    method: 'get'
  })
}

// 新增渗压监测
export function addSecure(data) {
  return request({
    url: '/matrix/osmometer',
    method: 'post',
    data: data
  })
}

// 修改渗压监测
export function updateSecure(data) {
  return request({
    url: '/matrix/osmometer',
    method: 'put',
    data: data
  })
}

// 删除渗压监测
export function delSecure(did) {
  return request({
    url: '/matrix/osmometer/' + did,
    method: 'delete'
  })
}
// 获取断面类型
export function listSections(query){
  return request({
    url: '/matrix/osmometer/listsections',
    method: 'get',
    params: query
  })
}
// 获取测站类型
export function listStation(query){
  return request({
    url: '/matrix/osmometer/liststation',
    method: 'get',
    params: query
  })
}

// 获取某一时刻，某一断面的渗压数据
export function getDataBySectionId(query){
  return request({
    url: '/matrix/osmometer/section',
    method: 'get',
    params: query
  })
}

