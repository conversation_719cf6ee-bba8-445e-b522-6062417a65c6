import request from '@/utils/request'

//管理平台首页-水资源统计
export function waterResource(query) {
  return request({
    url: '/matrix/shouye/waterResource',
    method: 'get',
    params: query
  })
}
//管理平台首页-消息待办
export function notice(query) {
  return request({
    url: '/matrix/shouye/notice',
    method: 'get',
    params: query
  })
}

//管理平台首页-没有泵站监测数据
export function pump(query) {
  return request({
    url: '/matrix/shouye/pump',
    method: 'get',
    params: query
  })
}

//管理平台首页-水库名称
export function rsrName(bid) {
  return request({
    url: '/matrix/wrp/'+bid,
    method: 'get',
  })
}

//管理平台首页-雨水情监测
export function waterAndRain(query) {
  return request({
    url: '/matrix/shouye/waterAndRain',
    method: 'get',
    params: query
  })
}
//管理平台首页-告警统计
export function alarm(query) {
  return request({
    url: '/matrix/shouye/alarm',
    method: 'get',
    params: query
  })
}
//管理平台首页-运行管护
export function management(query) {
  return request({
    url: '/matrix/shouye/management',
    method: 'get',
    params: query
  })
}
//管理平台首页-保障管理
export function guarantee(query) {
  return request({
    url: '/matrix/shouye/guarantee',
    method: 'get',
    params: query
  })
}
//管理平台首页-调度运用
export function wateranalyse(query) {
  return request({
    url: '/matrix/shouye/wateranalyse',
    method: 'get',
    params: query
  })
}
