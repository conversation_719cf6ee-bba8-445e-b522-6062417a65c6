<template>
  <div class="infoBox" :style="{left: left}">
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" width="180">
      </el-table-column>
      <el-table-column prop="dataValue" width="180">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        {
          name: '泵后瞬时流量',
          dataValue: '0.00'
        }, {
          name: '泵后累积流量',
          dataValue: '1341252'
        }, {
          name: '出水压力',
          dataValue: '0.49'
        }, {
          name: '出水浊度',
          dataValue: '0.32'
        },
        {
          name: '出水余氯',
          dataValue: '-0.00'
        },
        {
          name: '出水PH',
          dataValue: '7.82'
        },
        {
          name: '出水COD',
          dataValue: '-0.76'
        },
        {
          name: '出水氨氮',
          dataValue: '0.02'
        },
        {
          name: '进水电导率',
          dataValue: '1227.39'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.infoBox {
  position: absolute;
  right: 50px;
  top: 50px;
}
</style>