import request from '@/utils/request'

// 查询物资仓库列表
export function listWzWarehouse(query) {
  return request({
    url: '/matrix/wzWarehouse/list',
    method: 'get',
    params: query
  })
}

// 查询物资仓库详细
export function getWzWarehouse(id) {
  return request({
    url: '/matrix/wzWarehouse/' + id,
    method: 'get'
  })
}

// 新增物资仓库
export function addWzWarehouse(data) {
  return request({
    url: '/matrix/wzWarehouse',
    method: 'post',
    data: data
  })
}

// 修改物资仓库
export function updateWzWarehouse(data) {
  return request({
    url: '/matrix/wzWarehouse',
    method: 'put',
    data: data
  })
}

// 删除物资仓库
export function delWzWarehouse(id) {
  return request({
    url: '/matrix/wzWarehouse/' + id,
    method: 'delete'
  })
}
// 查询所有正常使用的行政区域列表
export function listNormalArea(params) {
  return request({
    url: '/matrix/water/area/listArea',
    method: 'get',
    params
  })
}
