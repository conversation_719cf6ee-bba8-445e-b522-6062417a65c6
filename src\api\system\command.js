import request from '@/utils/request'

// 查询捷径命令管理列表
export function listCommand(query) {
  return request({
    url: '/matrix/command/list',
    method: 'get',
    params: query
  })
}

// 查询捷径命令管理详细
export function getCommand(id) {
  return request({
    url: '/matrix/command/' + id,
    method: 'get'
  })
}

// 新增捷径命令管理
export function addCommand(data) {
  return request({
    url: '/matrix/command',
    method: 'post',
    data: data
  })
}

// 修改捷径命令管理
export function updateCommand(data) {
  return request({
    url: '/matrix/command',
    method: 'put',
    data: data
  })
}

// 删除捷径命令管理
export function delCommand(id) {
  return request({
    url: '/matrix/command/' + id,
    method: 'delete'
  })
}
