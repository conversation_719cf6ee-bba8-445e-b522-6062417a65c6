import request from '@/utils/request'

// 查询经费管理列表
export function listFund(query) {
  return request({
    url: '/matrix/fund/list',
    method: 'get',
    params: query
  })
}

// 查询经费管理详细
export function getFund(fid) {
  return request({
    url: '/matrix/fund/' + fid,
    method: 'get'
  })
}

// 新增经费管理
export function addFund(data) {
  return request({
    url: '/matrix/fund',
    method: 'post',
    data: data
  })
}

// 修改经费管理
export function updateFund(data) {
  return request({
    url: '/matrix/fund',
    method: 'put',
    data: data
  })
}

// 删除经费管理
export function delFund(fid) {
  return request({
    url: '/matrix/fund/' + fid,
    method: 'delete'
  })
}


