import request from '@/utils/request'

// 查询成员单位列表
export function listMember(query) {
  return request({
    url: '/matrix/member/list',
    method: 'get',
    params: query
  })
}

// 查询成员单位详细
export function getMember(mid) {
  return request({
    url: '/matrix/member/' + mid,
    method: 'get'
  })
}

// 新增成员单位
export function addMember(data) {
  return request({
    url: '/matrix/member',
    method: 'post',
    data: data
  })
}

// 修改成员单位
export function updateMember(data) {
  return request({
    url: '/matrix/member',
    method: 'put',
    data: data
  })
}

// 删除成员单位
export function delMember(mid) {
  return request({
    url: '/matrix/member/' + mid,
    method: 'delete'
  })
}
