import request from '@/utils/request'

// 查询水库水情列表
export function listWaterdetection(query) {
  return request({
    url: '/matrix/waterdetection/list',
    method: 'get',
    params: query
  })
}

// 查询水库水情详细
export function getWaterdetection(stcd) {
  return request({
    url: '/matrix/waterdetection/' + stcd,
    method: 'get'
  })
}

// 新增水库水情
export function addWaterdetection(data) {
  return request({
    url: '/matrix/waterdetection',
    method: 'post',
    data: data
  })
}

// 修改水库水情
export function updateWaterdetection(data) {
  return request({
    url: '/matrix/waterdetection',
    method: 'put',
    data: data
  })
}

// 删除水库水情
export function delWaterdetection(stcd) {
  return request({
    url: '/matrix/waterdetection/' + stcd,
    method: 'delete'
  })
}
// 查询最近一条数据
export function selectLast(query) {
  return request({
    url: '/matrix/waterdetection/selectlast',
    method: 'get',
    params: query
  })
}

// 水文特征
// 查询水库水文特征列表
export function listHych(query) {
  return request({
    url: '/matrix/hych/list',
    method: 'get',
    params: query
  })
}
/**
 * 告警记录
 */
export function listAlarm(query) {
  return request({
    url: '/matrix/waterdetection/alarm_status',
    method: 'get',
    params: query
  })
}

// 查询测站信息
export function stlist(query) {
  return request({
    url: '/matrix/wrp/stlist',
    method: 'get',
    params: query
  })
}
